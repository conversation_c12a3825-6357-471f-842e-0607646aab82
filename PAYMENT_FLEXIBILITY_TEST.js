// Test flexible payment system: appointment payments vs lab result payments
// This test verifies that the payment system correctly handles both types of payments

const BASE_URL = 'http://localhost:8080';

// Test data for different payment scenarios
const testPaymentData = {
    // Appointment payment (lịch hẹn payment)
    appointmentPayment: {
        appointmentId: 1,
        patientId: 1,
        staffId: 1,
        method: "CASH",
        amount: 100000.0,
        paymentDate: "2025-08-04T08:00:00"
        // labRequestId is null
    },
    
    // Lab result payment (thanh toán kết quả xét nghiệm)
    labResultPayment: {
        labRequestId: 1,
        patientId: 1,
        staffId: 1,
        method: "CASH",
        amount: 50000.0,
        paymentDate: "2025-08-04T08:00:00"
        // appointmentId is null
    },
    
    // Invalid: Both appointment and lab (should fail)
    bothPayment: {
        appointmentId: 1,
        labRequestId: 1,
        patientId: 1,
        staffId: 1,
        method: "CASH",
        amount: 100000.0,
        paymentDate: "2025-08-04T08:00:00"
    },
    
    // Invalid: Neither appointment nor lab (should fail)
    neitherPayment: {
        patientId: 1,
        staffId: 1,
        method: "CASH",
        amount: 100000.0,
        paymentDate: "2025-08-04T08:00:00"
        // Both appointmentId and labRequestId are null
    },
    
    // VNPay lab payment
    vnpayLabPayment: {
        labRequestId: 1,
        patientId: 1,
        method: "VNPAY",
        amount: 75000.0,
        paymentDate: "2025-08-04T08:00:00"
    }
};

async function testPaymentCreation(paymentData, testName) {
    console.log(`\n=== Testing ${testName} ===`);
    console.log('Request data:', JSON.stringify(paymentData, null, 2));
    
    try {
        const response = await fetch(`${BASE_URL}/api/payments`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': 'Bearer eyJhbGciOiJIUzI1NiJ9.***********************************************************************************.YOUR_TOKEN_HERE'
            },
            body: JSON.stringify(paymentData)
        });

        const responseText = await response.text();
        console.log(`Status: ${response.status}`);
        console.log(`Response: ${responseText}`);

        if (response.ok) {
            console.log(`✅ ${testName} - SUCCESS`);
            return JSON.parse(responseText);
        } else {
            console.log(`❌ ${testName} - FAILED (Expected for validation tests)`);
            console.log(`Error: ${responseText}`);
            return null;
        }
    } catch (error) {
        console.log(`❌ ${testName} - ERROR: ${error.message}`);
        return null;
    }
}

async function runFlexiblePaymentTests() {
    console.log("🧪 Flexible Payment System Tests");
    console.log("=================================");
    console.log("Testing the business rule: 'thanh toán kết quả xét nghiệm thì lịch hẹn null và ngược lại'");
    
    // Test 1: Appointment payment (valid scenario)
    await testPaymentCreation(testPaymentData.appointmentPayment, "Appointment Payment");
    
    // Test 2: Lab result payment (valid scenario)
    await testPaymentCreation(testPaymentData.labResultPayment, "Lab Result Payment");
    
    // Test 3: Both appointment and lab (should fail)
    await testPaymentCreation(testPaymentData.bothPayment, "Both Payment Types (Should Fail)");
    
    // Test 4: Neither appointment nor lab (should fail)
    await testPaymentCreation(testPaymentData.neitherPayment, "Neither Payment Type (Should Fail)");
    
    // Test 5: VNPay lab payment
    await testPaymentCreation(testPaymentData.vnpayLabPayment, "VNPay Lab Payment");
    
    console.log("\n🏁 Tests completed!");
    console.log("\nExpected Results:");
    console.log("✅ Appointment Payment: Should work if appointment exists and is CHECKED_IN");
    console.log("✅ Lab Result Payment: Should work if lab request exists");
    console.log("❌ Both Payment Types: Should fail with 'Không thể thanh toán cả lịch hẹn và xét nghiệm cùng lúc'");
    console.log("❌ Neither Payment Type: Should fail with 'Phải có ID lịch hẹn hoặc ID yêu cầu xét nghiệm'");
    console.log("✅ VNPay Lab Payment: Should work for lab payments via VNPay");
    
    console.log("\nBusiness Rule Validation:");
    console.log("- Appointment payments: appointmentId != null, labRequestId == null");
    console.log("- Lab result payments: labRequestId != null, appointmentId == null");
    console.log("- Mutual exclusion: Cannot have both types in one payment");
    console.log("- At least one required: Must have either appointment or lab request");
}

// Helper function to test specific scenarios
async function testSpecificScenario(scenarioName) {
    console.log(`\n🎯 Testing Specific Scenario: ${scenarioName}`);
    
    switch(scenarioName) {
        case 'appointment':
            await testPaymentCreation(testPaymentData.appointmentPayment, "Appointment Payment Only");
            break;
        case 'lab':
            await testPaymentCreation(testPaymentData.labResultPayment, "Lab Payment Only");
            break;
        case 'validation':
            await testPaymentCreation(testPaymentData.bothPayment, "Validation Test - Both Types");
            await testPaymentCreation(testPaymentData.neitherPayment, "Validation Test - Neither Type");
            break;
        default:
            console.log("Available scenarios: 'appointment', 'lab', 'validation'");
    }
}

// Uncomment to run all tests
// runFlexiblePaymentTests();

// Uncomment to test specific scenario
// testSpecificScenario('appointment');

// Export for manual testing
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        testPaymentData,
        testPaymentCreation,
        runFlexiblePaymentTests,
        testSpecificScenario
    };
}

// Test script for ARV Protocol Update
// Sử dụng để test việc cập nhật phác đồ ARV sau khi sửa lỗi transaction

const TEST_ARV_PROTOCOL_UPDATE = {
    testData: {
        protocolId: 12, // Thay đổi ID này theo dữ liệu thực tế
        updateData: {
            name: "Ph<PERSON>c đồ ARV cập nhật test",
            description: "<PERSON><PERSON> tả phác đồ đã được cập nhật",
            recommendation: "PREFERRED", // hoặc "ALTERNATIVE", "NOT_RECOMMENDED"
            treatmentLevel: "FIRST_LINE", // hoặc "SECOND_LINE", "THIRD_LINE"
            sideEffects: "Tác dụng phụ đã cập nhật",
            contraindications: "Chống chỉ định đã cập nhật",
            targetGroup: "ADULTS", // hoặc "CHILDREN", "PREGNANT_WOMEN", "HIV_RESISTANCE"
            active: true,
            arvProtocolMedicationsDTO: [
                {
                    medicationId: 1, // ID thuốc ARV có sẵn
                    dosage: "600mg",
                    frequency: "1 lần/ngày",
                    duration: "Liên tục",
                    sideEffects: "Có thể gây buồn nôn",
                    note: "Uống cùng với thức ăn"
                },
                {
                    medicationId: 2, // ID thuốc ARV khác
                    dosage: "300mg", 
                    frequency: "2 lần/ngày",
                    duration: "Liên tục",
                    sideEffects: "Có thể gây đau đầu",
                    note: "Uống vào buổi sáng và tối"
                }
            ]
        }
    },

    // Test case 1: Cập nhật thành công
    async testUpdateSuccess() {
        try {
            const response = await fetch(`http://localhost:8080/api/arv-protocol/${this.testData.protocolId}`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': 'Bearer YOUR_JWT_TOKEN' // Thay thế bằng token thực
                },
                body: JSON.stringify(this.testData.updateData)
            });

            if (response.ok) {
                const result = await response.json();
                console.log('✅ Cập nhật ARV Protocol thành công:', result);
                return result;
            } else {
                const error = await response.json();
                console.error('❌ Lỗi khi cập nhật ARV Protocol:', error);
                return null;
            }
        } catch (error) {
            console.error('❌ Lỗi network:', error);
            return null;
        }
    },

    // Test case 2: Kiểm tra dữ liệu sau khi cập nhật
    async testGetUpdatedProtocol() {
        try {
            const response = await fetch(`http://localhost:8080/api/arv-protocol/${this.testData.protocolId}`, {
                method: 'GET',
                headers: {
                    'Authorization': 'Bearer YOUR_JWT_TOKEN' // Thay thế bằng token thực
                }
            });

            if (response.ok) {
                const result = await response.json();
                console.log('✅ Lấy ARV Protocol sau khi cập nhật:', result);
                return result;
            } else {
                const error = await response.json();
                console.error('❌ Lỗi khi lấy ARV Protocol:', error);
                return null;
            }
        } catch (error) {
            console.error('❌ Lỗi network:', error);
            return null;
        }
    },

    // Chạy tất cả test
    async runAllTests() {
        console.log('🚀 Bắt đầu test ARV Protocol Update...');
        
        console.log('\n📝 Test 1: Cập nhật ARV Protocol');
        const updateResult = await this.testUpdateSuccess();
        
        if (updateResult) {
            console.log('\n📋 Test 2: Kiểm tra dữ liệu sau khi cập nhật');
            await this.testGetUpdatedProtocol();
        }
        
        console.log('\n✅ Hoàn thành test ARV Protocol Update');
    }
};

// Hướng dẫn sử dụng:
console.log(`
🔧 HƯỚNG DẪN TEST ARV PROTOCOL UPDATE:

1. Mở Developer Tools (F12) trong trình duyệt
2. Vào tab Console
3. Copy và paste toàn bộ code này vào console
4. Thay đổi các giá trị trong testData theo dữ liệu thực tế:
   - protocolId: ID của phác đồ ARV cần test
   - medicationId: ID của các thuốc ARV có sẵn
   - Authorization token: JWT token của STAFF/ADMIN

5. Chạy test:
   TEST_ARV_PROTOCOL_UPDATE.runAllTests();

📋 KIỂM TRA:
- Backend server đang chạy ở http://localhost:8080
- Đã login với role STAFF hoặc ADMIN
- Có dữ liệu ARV Protocol và ARV Medication trong database

🐛 NẾU GẶP LỖI:
- Kiểm tra Network tab để xem chi tiết request/response
- Kiểm tra Backend logs để xem lỗi transaction
- Đảm bảo các foreign key (medicationId) tồn tại trong database
`);

// Export để sử dụng trong Node.js nếu cần
if (typeof module !== 'undefined' && module.exports) {
    module.exports = TEST_ARV_PROTOCOL_UPDATE;
}

.confirm-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
}

.confirm-modal-content {
  font-family: 'Segoe UI', 'Roboto', 'Arial', sans-serif;
  background: #fff;
  border-radius: 10px;
  padding: 32px 32px 24px 32px;
  max-width: 400px;
  width: 100%;
  box-shadow: 0 4px 32px rgba(0, 0, 0, 0.15);
  text-align: center;
}

.confirm-modal-content h3 {
  font-family: 'Segoe UI', 'Roboto', 'Arial', sans-serif;
  margin-bottom: 1rem;
  font-size: 1.3rem;
  color: #222;
}

.confirm-message {
  font-family: 'Segoe UI', 'Roboto', 'Arial', sans-serif;
  font-size: 1.08rem;
  margin-bottom: 2rem;
  color: #333;
}

.confirm-actions {
  display: flex;
  justify-content: flex-end;
  gap: 16px;
}

.cancel-button,
.save-button {
  font-family: 'Segoe UI', 'Roboto', 'Arial', sans-serif;
  padding: 10px 24px;
  border-radius: 6px;
  font-size: 15px;
  font-weight: 500;
  border: none;
  cursor: pointer;
  transition: background 0.2s;
}

.cancel-button {
  background: #f5f5f5;
  color: #333;
}

.cancel-button:hover {
  background: #e0e0e0;
}

.save-button {
  background: #1a73e8;
  color: white;
}

.save-button:hover {
  background: #1557b0;
}
.arv-protocol {
    padding: 20px;
    max-width: 800px;
    margin: 0 auto;

    .medication-card {
        background: #fff;
        border-radius: 12px;
        padding: 24px;
        margin-bottom: 24px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

        h2 {
            color: #333;
            margin-bottom: 12px;
            font-size: 1.5rem;
        }

        p {
            color: #666;
            margin-bottom: 20px;
        }
    }

    .med-item {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 16px;
        border: 1px solid #eee;
        border-radius: 8px;
        margin-bottom: 12px;

        .med-info {
            display: flex;
            align-items: center;
            gap: 12px;

            .icon {
                color: #4a90e2;
                font-size: 24px;
            }

            div {
                display: flex;
                flex-direction: column;

                strong {
                    color: #333;
                    margin-bottom: 4px;
                }

                span {
                    color: #666;
                    font-size: 0.9rem;
                }
            }
        }

        .med-count {
            color: #4a90e2;
            font-weight: 500;
        }
    }

    .action-buttons {
        display: flex;
        gap: 12px;
        margin-top: 20px;

        button {
            padding: 10px 20px;
            border-radius: 6px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;

            &.btn-primary {
                background-color: #4a90e2;
                color: white;
                border: none;

                &:hover {
                    background-color: #357abd;
                }
            }

            &.btn-secondary {
                background-color: white;
                color: #4a90e2;
                border: 1px solid #4a90e2;

                &:hover {
                    background-color: #f5f8ff;
                }
            }
        }
    }

    .appointment-item {
        display: flex;
        align-items: center;
        gap: 16px;
        padding: 16px;
        border: 1px solid #eee;
        border-radius: 8px;
        margin-bottom: 16px;

        .icon {
            color: #4a90e2;
            font-size: 24px;
        }

        div {
            display: flex;
            flex-direction: column;

            strong {
                color: #333;
                margin-bottom: 4px;
            }

            span {
                color: #666;
                font-size: 0.9rem;
                margin-bottom: 2px;
            }
        }
    }

    .btn-primary {
        width: 100%;
        padding: 12px;
        background-color: #4a90e2;
        color: white;
        border: none;
        border-radius: 6px;
        font-weight: 500;
        cursor: pointer;
        transition: background-color 0.3s ease;

        &:hover {
            background-color: #357abd;
        }
    }
}
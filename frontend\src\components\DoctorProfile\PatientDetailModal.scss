.patient-detail-modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    padding: 20px;
}

.patient-detail-modal {
    background: #fff;
    border-radius: 12px;
    width: 100%;
    max-width: 800px;
    max-height: 90vh;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);

    &__header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 24px 24px 0 24px;
        border-bottom: 1px solid #e0e0e0;

        h2 {
            margin: 0;
            color: #333;
            font-size: 24px;
            font-weight: 600;
        }
    }

    &__close-btn {
        background: none;
        border: none;
        color: #666;
        cursor: pointer;
        padding: 8px;
        border-radius: 50%;
        transition: all 0.3s ease;

        &:hover {
            background: #f0f0f0;
            color: #333;
        }
    }

    &__content {
        flex: 1;
        overflow-y: auto;
        padding: 24px;
        scroll-behavior: smooth;
        -webkit-overflow-scrolling: touch;
    }

    &__section {
        margin-bottom: 32px;

        &:last-child {
            margin-bottom: 0;
        }
    }

    &__section-title {
        display: flex;
        align-items: center;
        gap: 8px;
        margin: 0 0 16px 0;
        color: #333;
        font-size: 18px;
        font-weight: 600;
        padding-bottom: 8px;
        border-bottom: 2px solid #007bff;
    }

    &__info-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 16px;
    }

    &__info-item {
        display: flex;
        flex-direction: column;
        gap: 4px;

        label {
            font-weight: 600;
            color: #666;
            font-size: 14px;
        }

        span {
            color: #333;
            font-size: 16px;
            padding: 8px 12px;
            background: #f8f9fa;
            border-radius: 6px;
            border: 1px solid #e0e0e0;
        }
    }

    &__stats {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
        gap: 16px;
    }

    &__stat-item {
        text-align: center;
        padding: 16px;
        background: #f8f9fa;
        border-radius: 8px;
        border: 1px solid #e0e0e0;
    }

    &__stat-number {
        display: block;
        font-size: 24px;
        font-weight: 700;
        color: #007bff;
        margin-bottom: 4px;
    }

    &__stat-label {
        font-size: 12px;
        color: #666;
        font-weight: 500;
    }

    &__appointments {
        margin-top: 12px;

        table.patient-detail-modal__lab-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 8px;
            background: #fff;
            border-radius: 6px;
            overflow: hidden;
            box-shadow: 0 1px 2px rgba(0,0,0,0.03);

            th, td {
                padding: 8px 12px;
                text-align: left;
                font-size: 15px;
            }

            th {
                background: #f7f9fa;
                font-weight: 600;
                color: #222;
                border-bottom: 2px solid #e0e7ef;
            }

            tr:not(:last-child) td {
                border-bottom: 1px solid #f0f0f0;
            }

            td {
                color: #222;
            }
        }

        .patient-detail-modal__empty {
            color: #888;
            font-style: italic;
            padding: 12px 0;
            text-align: center;
        }
    }

    &__appointment-item {
        background: #f8f9fa;
        border: 1px solid #e0e0e0;
        border-radius: 8px;
        padding: 16px;
        margin-bottom: 12px;

        &:last-child {
            margin-bottom: 0;
        }
    }

    &__appointment-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 8px;
    }

    &__appointment-date {
        display: flex;
        align-items: center;
        gap: 6px;
        color: #333;
        font-weight: 500;
        font-size: 14px;
    }

    &__appointment-time {
        color: #666;
        font-weight: 500;
        font-size: 14px;
    }

    &__appointment-status {
        padding: 4px 8px;
        border-radius: 4px;
        font-size: 11px;
        font-weight: 600;
        text-transform: uppercase;

        &.confirmed {
            background: #d4edda;
            color: #155724;
        }

        &.pending {
            background: #fff3cd;
            color: #856404;
        }

        &.completed {
            background: #d1ecf1;
            color: #0c5460;
        }

        &.cancelled {
            background: #f8d7da;
            color: #721c24;
        }

        &.default {
            background: #e2e3e5;
            color: #383d41;
        }
    }

    &__appointment-notes,
    &__appointment-service {
        display: flex;
        align-items: center;
        gap: 6px;
        margin-top: 8px;
        font-size: 13px;
        color: #666;

        svg {
            color: #999;
        }
    }

    &__empty {
        text-align: center;
        padding: 40px;
        color: #666;

        p {
            margin: 0;
            font-size: 16px;
        }
    }

    &__notes-input {
        width: 100%;
        padding: 12px;
        border: 1px solid #ddd;
        border-radius: 6px;
        font-size: 14px;
        resize: vertical;
        min-height: 80px;

        &:focus {
            outline: none;
            border-color: #007bff;
            box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
        }

        &::placeholder {
            color: #999;
        }
    }

    &__footer {
        display: flex;
        justify-content: flex-end;
        gap: 12px;
        padding: 24px;
        border-top: 1px solid #e0e0e0;
        background: #f8f9fa;
    }

    &__btn {
        padding: 10px 20px;
        border: none;
        border-radius: 6px;
        font-size: 14px;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.3s ease;

        &--primary {
            background: #007bff;
            color: #fff;

            &:hover {
                background: #0056b3;
            }
        }

        &--secondary {
            background: #6c757d;
            color: #fff;

            &:hover {
                background: #545b62;
            }
        }
    }

    &__active-treatment-section {
        background: #e6f7ff;
        border: 1.5px solid #1890ff;
        border-radius: 10px;
        padding: 18px 18px 10px 18px;
        margin-bottom: 24px;
        box-shadow: 0 2px 8px rgba(24,144,255,0.08);
        
        .patient-detail-modal__section-title {
            color: #1890ff;
            font-size: 20px;
            font-weight: 700;
            svg {
                color: #1890ff;
                font-size: 22px;
            }
        }
        .patient-detail-modal__appointment-item {
            background: #f0faff;
            border: 1px solid #b7e0ff;
            border-radius: 8px;
            margin-bottom: 12px;
            padding: 14px 16px;
            box-shadow: 0 1px 4px rgba(24,144,255,0.04);
        }
        .patient-detail-modal__appointment-header {
            margin-bottom: 6px;
            .patient-detail-modal__appointment-date,
            .patient-detail-modal__appointment-time {
                color: #0050b3;
                font-weight: 600;
            }
        }
        .patient-detail-modal__lab-result {
            font-size: 15px;
            color: #222;
            strong {
                color: #096dd9;
                font-weight: 600;
            }
        }
        .patient-detail-modal__appointment-status {
            &.confirmed, &.completed {
                background: #d6f5e9;
                color: #1890ff;
            }
            &.pending {
                background: #fffbe6;
                color: #faad14;
            }
            &.cancelled {
                background: #fff1f0;
                color: #ff4d4f;
            }
        }
        .patient-detail-modal__empty {
            color: #888;
            font-style: italic;
            padding: 12px 0;
            text-align: center;
        }
    }
}

.patient-detail-modal__lab-item {
    background: #fafbfc;
    border-radius: 8px;
    padding: 12px 16px;
    margin-bottom: 10px;
    box-shadow: 0 1px 2px rgba(0,0,0,0.03);
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
}

.patient-detail-modal__lab-header {
    display: flex;
    align-items: center;
    gap: 12px;
    font-size: 15px;
    color: #222;
}

.patient-detail-modal__lab-date {
    display: flex;
    align-items: center;
    gap: 4px;
    color: #222;
}

.patient-detail-modal__lab-type {
    display: flex;
    align-items: center;
    gap: 4px;
    color: #666;
}

.patient-detail-modal__lab-result {
    font-size: 16px;
    margin-top: 4px;
    color: #222;
    font-weight: 500;
}

@media (max-width: 768px) {
    .patient-detail-modal-overlay {
        padding: 10px;
    }

    .patient-detail-modal {
        max-height: 95vh;

        &__header {
            padding: 16px 16px 0 16px;

            h2 {
                font-size: 20px;
            }
        }

        &__content {
            padding: 16px;
        }

        &__info-grid {
            grid-template-columns: 1fr;
        }

        &__stats {
            grid-template-columns: repeat(2, 1fr);
        }

        &__appointment-header {
            flex-direction: column;
            align-items: flex-start;
            gap: 8px;
        }

        &__footer {
            padding: 16px;
            flex-direction: column;

            .patient-detail-modal__btn {
                width: 100%;
            }
        }
    }
}
package com.group7.hivcare.hivtreatmentmedicalservicesystem.treatmenthistory.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PrescriptionHistoryDTO {
    private Integer id;
    private Integer treatmentPlanId;
    private Integer prescriptionId;
    private String changeReason;
    private LocalDateTime createdAt;
    
    // Thông tin bệnh nhân và bác sĩ
    private Integer patientId;
    private String patientName;
    private Integer doctorId;
    private String doctorName;
    
    // Thông tin thuốc từ đơn thuốc cũ
    private List<MedicationInfo> oldMedications;
    
    // Inner class để chứa thông tin thuốc
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class MedicationInfo {
        private Integer medicationId;
        private String name;
        private String dosage; // Chỉ định số lượng thuốc cần dùng trong một lần
        private String frequency; // Chỉ định mỗi ngày dùng bao nhiêu lần
        private Integer durationDays; // Chỉ định sử dụng thuốc liên tục trong bao nhiêu ngày
        private String notes;
    }
} 
-- Script để xóa dịch vụ "Xét Nghiệm HIV" khỏi database
-- Chạy script này trong MySQL Workbench hoặc terminal MySQL

USE hiv_treatment_db;

-- Kiểm tra dịch vụ cần xóa trước khi xóa
SELECT * FROM medical_services WHERE name = 'Xét Nghiệm HIV';

-- <PERSON><PERSON><PERSON> các appointment liên quan đến dịch vụ này (nếu có)
-- Cảnh báo: Thao tác này sẽ xóa tất cả lịch hẹn liên quan đến dịch vụ "Xét Nghiệm HIV"
-- Uncommnet dòng dưới nếu bạn muốn xóa luôn các appointment liên quan
-- DELETE FROM appointments WHERE medical_service_id = (SELECT id FROM medical_services WHERE name = 'Xét Nghiệm HIV');

-- <PERSON><PERSON><PERSON> dịch vụ "<PERSON><PERSON><PERSON> Nghiệm HIV"
DELETE FROM medical_services WHERE name = '<PERSON><PERSON><PERSON> Nghiệm HIV';

-- Kiểm tra sau khi xóa
SELECT * FROM medical_services ORDER BY id;

-- <PERSON><PERSON><PERSON> thị thông báo
SELECT 'Đã xóa dịch vụ Xét Nghiệm HIV thành công!' as message;

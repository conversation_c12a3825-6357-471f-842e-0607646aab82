.drug-management-table {
    padding: 1rem;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    margin-top: 20px;
}

.table-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 1rem;
    flex-wrap: wrap;
    gap: 1rem;
}

.table-header h2 {
    margin: 0;
    font-size: 1.5rem;
    color: #333;
}

.header-actions {
    display: flex;
    align-items: center;
    gap: 1rem;
    flex-wrap: wrap;
}

.search-input {
    padding: 0.5rem 1rem;
    border: 1px solid #ccc;
    border-radius: 4px;
    font-size: 1rem;
    min-width: 260px;
}

.add-button {
    background: #4CAF50;
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 4px;
    cursor: pointer;
    font-size: 1rem;
    font-weight: 500;
    transition: background-color 0.3s;
}

.add-button:hover {
    background: #45a049;
}

.drug-management-table table {
    width: 100%;
    border-collapse: collapse;
    background: #fff;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    border-radius: 8px;
    overflow: hidden;
    table-layout: fixed;
}

.drug-management-table th,
.drug-management-table td {
    padding: 0.75rem 1rem;
    border: none;
    text-align: left;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.drug-management-table th {
    background: #f5f5f5;
    font-weight: 600;
    color: #333;
}

.drug-management-table tr:hover {
    background-color: #f9f9f9;
}

.edit-button,
.delete-button {
    padding: 0.4rem 0.8rem;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.875rem;
    margin-right: 0.5rem;
    transition: background-color 0.3s;
}

.edit-button {
    background: #2196F3;
    color: white;
}

.edit-button:hover {
    background: #1976D2;
}

.delete-button {
    background: #f44336;
    color: white;
}

.delete-button:hover {
    background: #d32f2f;
}

.loading {
    text-align: center;
    padding: 2rem;
    color: #666;
}

.error-message {
    background: #ffebee;
    color: #c62828;
    padding: 1rem;
    border-radius: 4px;
    margin-bottom: 1rem;
}

// Modal styles
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.modal-content {
    background: white;
    border-radius: 8px;
    padding: 0;
    max-width: 700px;
    width: 99%;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.7rem 1.2rem 0.7rem 1.2rem;
    border-bottom: 1px solid #e0e0e0;
}

.modal-header h3 {
    margin: 0;
    font-size: 1.25rem;
    color: #333;
}

.close-button {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: #666;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: background-color 0.3s;
}

.close-button:hover {
    background: #f5f5f5;
}

.service-form {
    padding: 0.7rem 1rem;
    overflow: visible;
}

.form-group {
    margin-bottom: 0.5rem;
    display: flex;
    align-items: flex-start;
    gap: 10px;
    flex-wrap: nowrap;
}

.form-group label {
    min-width: 110px;
    max-width: 130px;
    width: 110px;
    margin-bottom: 0 !important;
    font-weight: 500;
    color: #333;
    text-align: right;
    align-self: flex-start;
    white-space: nowrap;
    line-height: 38px;
}
.form-group input,
.form-group select,
.form-group textarea {
    min-width: 0;
    width: 100%;
    flex: 1 1 0;
    box-sizing: border-box;
    margin-top: 0 !important;
}
.form-group input,
.form-group select,
.form-group textarea {
    min-width: 0;
    width: 100%;
    flex: 1 1 0;
    box-sizing: border-box;
}

.form-group input,
.form-group select,
.form-group textarea {
    flex: 1;
    padding: 0.6rem 0.75rem;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 1rem;
    box-sizing: border-box;
    transition: border-color 0.3s;
}

.form-group input:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #2196F3;
}

.form-group textarea {
    resize: vertical;
    min-height: 80px;
}

.form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 1rem;
    margin-top: 2rem;
    padding-top: 1rem;
    border-top: 1px solid #e0e0e0;
}

.cancel-button,
.save-button {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 1rem;
    font-weight: 500;
    transition: background-color 0.3s;
}

.cancel-button {
    background: #f5f5f5;
    color: #333;
}

.cancel-button:hover {
    background: #e0e0e0;
}

.save-button {
    background: #4CAF50;
    color: white;
}

.save-button:hover {
    background: #45a049;
}

.save-button:disabled {
    background: #ccc;
    cursor: not-allowed;
}

.drug-management-table th:nth-child(1),
.drug-management-table td:nth-child(1) {
    width: 60px;
    min-width: 60px;
    max-width: 60px;
}

.drug-management-table th:nth-child(2),
.drug-management-table td:nth-child(2) {
    width: 100px;
    min-width: 80px;
    max-width: 120px;
}

.drug-management-table th:nth-child(3),
.drug-management-table td:nth-child(3) {
    width: 140px;
    min-width: 120px;
    max-width: 180px;
}

.drug-management-table th:nth-child(4),
.drug-management-table td:nth-child(4) {
    width: 320px;
    min-width: 220px;
    max-width: 400px;
}
// Test Active Schedule Filter for Doctor Leave Request
// This test verifies that only ACTIVE schedules are shown in the leave request form

const testScheduleData = [
    { id: 1, date: '2025-08-05', startTime: '08:00:00', endTime: '09:00:00', status: 'ACTIVE' },
    { id: 2, date: '2025-08-05', startTime: '10:00:00', endTime: '11:00:00', status: 'CANCELLED' },
    { id: 3, date: '2025-08-06', startTime: '14:00:00', endTime: '15:00:00', status: 'ACTIVE' },
    { id: 4, date: '2025-08-03', startTime: '08:00:00', endTime: '09:00:00', status: 'ACTIVE' }, // Past date
    { id: 5, date: '2025-08-07', startTime: '16:00:00', endTime: '17:00:00', status: 'INACTIVE' },
    { id: 6, date: '2025-08-08', startTime: '09:00:00', endTime: '10:00:00', status: 'ACTIVE' }
];

// Helper function to parse date like in the component
function parseLocalDate(dateStr) {
    const [year, month, day] = dateStr.split('-').map(Number);
    return new Date(year, month - 1, day);
}

// Helper function to get start of week
function getStartOfWeek(date) {
    const d = new Date(date);
    const day = d.getDay();
    const diff = d.getDate() - ((day === 0 ? 7 : day) - 1);
    d.setDate(diff);
    d.setHours(0, 0, 0, 0);
    return d;
}

function testActiveScheduleFiltering() {
    console.log("🧪 Testing Active Schedule Filtering for Leave Request");
    console.log("=====================================================");
    
    // Simulate current week (assuming today is 2025-08-04)
    const today = new Date(2025, 7, 4); // August 4, 2025
    const weekStart = getStartOfWeek(today);
    
    console.log(`Today: ${today.toLocaleDateString()}`);
    console.log(`Week Start: ${weekStart.toLocaleDateString()}`);
    
    // Filter like in the component
    const todayForComparison = new Date(today);
    todayForComparison.setHours(0, 0, 0, 0);
    
    const activeSlotsForLeaveRequest = testScheduleData.filter(s => {
        const d = parseLocalDate(s.date);
        return s.status === 'ACTIVE' && d >= todayForComparison && d >= weekStart && d < new Date(weekStart.getTime() + 7 * 86400000);
    });
    
    console.log("\n📋 All Schedules:");
    testScheduleData.forEach(s => {
        const date = parseLocalDate(s.date);
        const isPast = date < todayForComparison;
        const isInWeek = date >= weekStart && date < new Date(weekStart.getTime() + 7 * 86400000);
        console.log(`  ID: ${s.id}, Date: ${s.date}, Status: ${s.status}, Past: ${isPast}, InWeek: ${isInWeek}`);
    });
    
    console.log("\n✅ Filtered ACTIVE Schedules for Leave Request:");
    if (activeSlotsForLeaveRequest.length > 0) {
        activeSlotsForLeaveRequest.forEach(s => {
            console.log(`  ✓ ID: ${s.id}, Date: ${s.date}, Time: ${s.startTime.slice(0,5)} - ${s.endTime.slice(0,5)}, Status: ${s.status}`);
        });
    } else {
        console.log("  ⚠️ No ACTIVE schedules available for leave request");
    }
    
    console.log("\n❌ Excluded Schedules (should NOT appear in leave request form):");
    const excludedSchedules = testScheduleData.filter(s => !activeSlotsForLeaveRequest.includes(s));
    excludedSchedules.forEach(s => {
        const date = parseLocalDate(s.date);
        const isPast = date < todayForComparison;
        const isInWeek = date >= weekStart && date < new Date(weekStart.getTime() + 7 * 86400000);
        const reason = !isInWeek ? 'Not in current week' : 
                      isPast ? 'Past date' : 
                      s.status !== 'ACTIVE' ? `Status: ${s.status}` : 'Unknown';
        console.log(`  ✗ ID: ${s.id}, Date: ${s.date}, Status: ${s.status}, Reason: ${reason}`);
    });
    
    console.log("\n📊 Summary:");
    console.log(`Total schedules: ${testScheduleData.length}`);
    console.log(`ACTIVE schedules available for leave request: ${activeSlotsForLeaveRequest.length}`);
    console.log(`Excluded schedules: ${excludedSchedules.length}`);
    
    // Validation
    const expectedActiveIds = [1, 3, 6]; // IDs that should be included
    const actualActiveIds = activeSlotsForLeaveRequest.map(s => s.id);
    
    console.log("\n🔍 Validation:");
    console.log(`Expected IDs: [${expectedActiveIds.join(', ')}]`);
    console.log(`Actual IDs: [${actualActiveIds.join(', ')}]`);
    
    const isCorrect = expectedActiveIds.every(id => actualActiveIds.includes(id)) && 
                      actualActiveIds.every(id => expectedActiveIds.includes(id));
    
    if (isCorrect) {
        console.log("✅ Filter working correctly!");
    } else {
        console.log("❌ Filter has issues!");
    }
    
    return {
        totalSchedules: testScheduleData.length,
        filteredSchedules: activeSlotsForLeaveRequest.length,
        excludedSchedules: excludedSchedules.length,
        isFilterCorrect: isCorrect,
        activeSlots: activeSlotsForLeaveRequest
    };
}

// Test different scenarios
function testEdgeCases() {
    console.log("\n🧪 Testing Edge Cases");
    console.log("=====================");
    
    // Test 1: No ACTIVE schedules
    const noActiveSchedules = [
        { id: 1, date: '2025-08-05', startTime: '08:00:00', endTime: '09:00:00', status: 'CANCELLED' },
        { id: 2, date: '2025-08-06', startTime: '10:00:00', endTime: '11:00:00', status: 'INACTIVE' }
    ];
    
    console.log("\n1. No ACTIVE schedules test:");
    const filtered1 = noActiveSchedules.filter(s => s.status === 'ACTIVE');
    console.log(`Result: ${filtered1.length === 0 ? '✅ Correctly shows no schedules' : '❌ Should show no schedules'}`);
    
    // Test 2: Only past ACTIVE schedules
    const pastActiveSchedules = [
        { id: 1, date: '2025-08-01', startTime: '08:00:00', endTime: '09:00:00', status: 'ACTIVE' },
        { id: 2, date: '2025-08-02', startTime: '10:00:00', endTime: '11:00:00', status: 'ACTIVE' }
    ];
    
    console.log("\n2. Only past ACTIVE schedules test:");
    const today = new Date(2025, 7, 4);
    const todayForComparison = new Date(today);
    todayForComparison.setHours(0, 0, 0, 0);
    
    const filtered2 = pastActiveSchedules.filter(s => {
        const d = parseLocalDate(s.date);
        return s.status === 'ACTIVE' && d >= todayForComparison;
    });
    console.log(`Result: ${filtered2.length === 0 ? '✅ Correctly filters out past dates' : '❌ Should filter out past dates'}`);
    
    // Test 3: All ACTIVE future schedules
    const allActiveFuture = [
        { id: 1, date: '2025-08-05', startTime: '08:00:00', endTime: '09:00:00', status: 'ACTIVE' },
        { id: 2, date: '2025-08-06', startTime: '10:00:00', endTime: '11:00:00', status: 'ACTIVE' },
        { id: 3, date: '2025-08-07', startTime: '14:00:00', endTime: '15:00:00', status: 'ACTIVE' }
    ];
    
    console.log("\n3. All ACTIVE future schedules test:");
    const filtered3 = allActiveFuture.filter(s => {
        const d = parseLocalDate(s.date);
        return s.status === 'ACTIVE' && d >= todayForComparison;
    });
    console.log(`Result: ${filtered3.length === 3 ? '✅ All schedules included' : '❌ Should include all schedules'}`);
}

// Run tests
console.log("Running Active Schedule Filter Tests...\n");
const results = testScheduleFiltering();
testEdgeCases();

console.log("\n🏁 Test Complete!");
console.log("==================");
console.log("The filter now ensures that:");
console.log("✅ Only ACTIVE status schedules are shown");
console.log("✅ Only current and future dates are shown");
console.log("✅ Only schedules within the current week are shown");
console.log("✅ Clear message when no ACTIVE schedules available");
console.log("✅ Status indicator in dropdown options");

// Export for manual testing
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        testScheduleFiltering,
        testEdgeCases,
        testScheduleData
    };
}

package com.group7.hivcare.hivtreatmentmedicalservicesystem.treatmenthistory.service.impl;

import com.group7.hivcare.hivtreatmentmedicalservicesystem.common.entity.PatientTreatmentPlan;
import com.group7.hivcare.hivtreatmentmedicalservicesystem.common.entity.Prescription;
import com.group7.hivcare.hivtreatmentmedicalservicesystem.common.entity.PrescriptionHistory;
import com.group7.hivcare.hivtreatmentmedicalservicesystem.common.repository.PatientTreatmentPlanRepository;
import com.group7.hivcare.hivtreatmentmedicalservicesystem.common.repository.PrescriptionHistoryRepository;
import com.group7.hivcare.hivtreatmentmedicalservicesystem.common.repository.PrescriptionRepository;
import com.group7.hivcare.hivtreatmentmedicalservicesystem.treatmenthistory.dto.PrescriptionHistoryDTO;
import com.group7.hivcare.hivtreatmentmedicalservicesystem.treatmenthistory.mapper.PrescriptionHistoryMapper;
import com.group7.hivcare.hivtreatmentmedicalservicesystem.treatmenthistory.service.PrescriptionHistoryService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.util.List;

@Service
@RequiredArgsConstructor
public class PrescriptionHistoryServiceImpl implements PrescriptionHistoryService {

    private final PrescriptionHistoryRepository prescriptionHistoryRepository;
    private final PatientTreatmentPlanRepository treatmentPlanRepository;
    private final PrescriptionRepository prescriptionRepository;
    private final PrescriptionHistoryMapper mapper;

    @Override
    public List<PrescriptionHistoryDTO> getByTreatmentPlanId(Integer treatmentPlanId) {
        List<PrescriptionHistory> histories = prescriptionHistoryRepository.findByTreatmentPlanIdOrderByCreatedAtDesc(treatmentPlanId);
        return mapper.toDTOList(histories);
    }

    @Override
    public List<PrescriptionHistoryDTO> getByPatientId(Integer patientId) {
        List<PrescriptionHistory> histories = prescriptionHistoryRepository.findByPatientIdOrderByCreatedAtDesc(patientId);
        return mapper.toDTOList(histories);
    }

    @Override
    public List<PrescriptionHistoryDTO> getByDoctorId(Integer doctorId) {
        List<PrescriptionHistory> histories = prescriptionHistoryRepository.findByDoctorIdOrderByCreatedAtDesc(doctorId);
        return mapper.toDTOList(histories);
    }

    @Override
    public List<PrescriptionHistoryDTO> getByPrescriptionId(Integer prescriptionId) {
        List<PrescriptionHistory> histories = prescriptionHistoryRepository.findByPrescriptionIdOrderByCreatedAtDesc(prescriptionId);
        return mapper.toDTOList(histories);
    }

    @Override
    public List<PrescriptionHistoryDTO> getByPatientIdAndDateRange(Integer patientId, LocalDate startDate, LocalDate endDate) {
        List<PrescriptionHistory> histories = prescriptionHistoryRepository.findByPatientIdAndDateRange(patientId, startDate, endDate);
        return mapper.toDTOList(histories);
    }

    @Override
    public List<PrescriptionHistoryDTO> getByChangeReason(String changeReason) {
        List<PrescriptionHistory> histories = prescriptionHistoryRepository.findByChangeReasonContainingIgnoreCaseOrderByCreatedAtDesc(changeReason);
        return mapper.toDTOList(histories);
    }
} 
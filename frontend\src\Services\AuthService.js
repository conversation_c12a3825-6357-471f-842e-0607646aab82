import api from './api';

/**
 * <PERSON><PERSON><PERSON> yêu cầu đăng nhập đến backend
 * @param {string} email - Email của người dùng
 * @param {string} password - M<PERSON><PERSON> khẩu
 * @returns {Promise}
 */
export const login = (email, password) => {
    return api.post('/api/auth/login', {
        email,
        password
    });
};

/**
 * Đăng xuất
 * @returns {Promise}
 */
export const logout = () => {
    // Clear all auth-related items from localStorage
    localStorage.removeItem('token');
    localStorage.removeItem('username');
    localStorage.removeItem('role');
    localStorage.removeItem('rememberMe');
};

/**
 * G<PERSON>i yêu cầu đăng ký tài khoản đến backend
 * @param {Object} userData - Thông tin đăng ký của người dùng
 * @param {string} userData.email - Email
 * @param {string} userData.password - M<PERSON><PERSON> khẩu
 * @param {string} userData.fullName - Họ và tên
 * @returns {Promise}
 */
export const register = (userData) => {
    return api.post('/api/auth/register', userData);
};

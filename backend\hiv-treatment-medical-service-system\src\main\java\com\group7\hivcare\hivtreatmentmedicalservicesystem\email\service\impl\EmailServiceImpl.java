package com.group7.hivcare.hivtreatmentmedicalservicesystem.email.service.impl;

import com.group7.hivcare.hivtreatmentmedicalservicesystem.email.service.EmailService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.mail.SimpleMailMessage;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.stereotype.Service;

@Service
public class EmailServiceImpl implements EmailService {

    @Autowired
    private JavaMailSender emailSender;

    @Override
    public void sendVerificationEmail(String to, String verificationCode) {
        SimpleMailMessage message = new SimpleMailMessage();
        message.setTo(to);
        message.setSubject("Xác nhận đăng ký tài khoản");
        message.setText("Mã xác nhận của bạn là: " + verificationCode + "\n\n" +
                "Mã này sẽ hết hạn sau 10 phút.\n" +
                "Nếu bạn không yêu cầu mã này, vui lòng bỏ qua email này.");
        
        emailSender.send(message);
    }

    @Override
    public void sendRevisitAppointmentCreatedEmail(String to, String patientName, String doctorName, String revisitDate, String notes) {
        SimpleMailMessage message = new SimpleMailMessage();
        message.setTo(to);
        message.setSubject("Thông báo lịch hẹn tái khám");
        
        StringBuilder content = new StringBuilder();
        content.append("Kính gửi ").append(patientName).append(",\n\n");
        content.append("Bác sĩ ").append(doctorName).append(" đã tạo lịch hẹn tái khám cho bạn.\n\n");
        content.append("Chi tiết lịch hẹn:\n");
        content.append("- Ngày tái khám: ").append(revisitDate).append("\n");
        content.append("- Bác sĩ phụ trách: ").append(doctorName).append("\n");
        if (notes != null && !notes.trim().isEmpty()) {
            content.append("- Ghi chú: ").append(notes).append("\n");
        }
        content.append("\nVui lòng đến đúng ngày giờ đã hẹn.\n");
        content.append("Nếu có thay đổi, vui lòng liên hệ với phòng khám.\n\n");
        content.append("Trân trọng,\n");
        content.append("Hệ thống chăm sóc HIV");
        
        message.setText(content.toString());
        emailSender.send(message);
    }

    @Override
    public void sendRevisitAppointmentReminderEmail(String to, String patientName, String doctorName, String revisitDate, String notes) {
        SimpleMailMessage message = new SimpleMailMessage();
        message.setTo(to);
        message.setSubject("Nhắc nhở lịch hẹn tái khám - Ngày mai");
        
        StringBuilder content = new StringBuilder();
        content.append("Kính gửi ").append(patientName).append(",\n\n");
        content.append("Đây là thông báo nhắc nhở về lịch hẹn tái khám của bạn.\n\n");
        content.append("Chi tiết lịch hẹn:\n");
        content.append("- Ngày tái khám: ").append(revisitDate).append(" (NGÀY MAI)\n");
        content.append("- Bác sĩ phụ trách: ").append(doctorName).append("\n");
        if (notes != null && !notes.trim().isEmpty()) {
            content.append("- Ghi chú: ").append(notes).append("\n");
        }
        content.append("\nVui lòng chuẩn bị:\n");
        content.append("- Đến đúng giờ hẹn\n");
        content.append("- Mang theo các loại thuốc đang sử dụng\n");
        content.append("- Chuẩn bị các câu hỏi cần tư vấn\n\n");
        content.append("Nếu không thể đến, vui lòng liên hệ với phòng khám để sắp xếp lại.\n\n");
        content.append("Trân trọng,\n");
        content.append("Hệ thống chăm sóc HIV");
        
        message.setText(content.toString());
        emailSender.send(message);
    }
} 
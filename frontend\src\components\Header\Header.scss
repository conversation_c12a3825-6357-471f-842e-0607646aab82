.header {
    background: white;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;

    .container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 0 20px;
    }

    .header-content {
        display: flex;
        align-items: center;
        justify-content: space-between;
        height: 70px;
        white-space: nowrap;
    }

    .logo {
        display: flex;
        align-items: center;
        font-size: 0rem;
        font-weight: 600;
        color: #333;
        text-decoration: none;
        flex-shrink: 0;

        .logo-image {
            height: 90px;
            width: auto;
            padding-left: 10px;
            margin-right: 10px;
            object-fit: contain;
            transition: transform 0.3s ease;
        }

        .logo-text {
            font-weight: 600;
            color: #333;
        }

        &:hover {
            .logo-text {
                color: #007bff;
            }

            .logo-image {
                transform: scale(1.05);
            }
        }
    }

    .nav {
        flex: 1;
        display: flex;
        justify-content: center;

        .nav-list {
            display: flex;
            list-style: none;
            margin: 0;
            padding: 0;
            gap: 25px;
            align-items: center;

            li a {
                text-decoration: none;
                color: #333;
                font-weight: 500;
                font-size: 0.85rem;
                transition: color 0.3s ease;
                padding: 5px 0;
                white-space: nowrap;

                &:hover {
                    color: #007bff;
                }
            }
        }
    }

    .header-actions {
        display: flex;
        align-items: center;
        gap: 12px;
        flex-shrink: 0;


        .login-btn,
        .signup-btn {
            text-decoration: none;
            padding: 6px 14px;
            border-radius: 4px;
            font-size: 0.85rem;
            font-weight: 500;
            transition: all 0.3s ease;
            white-space: nowrap;
            text-align: center;
        }

        .login-btn {
            color: #333;
            background: transparent;

            &:hover {
                color: #007bff;
            }
        }

        .signup-btn {
            background: #333;
            color: white;

            &:hover {
                background: #555;
                transform: translateY(-1px);
            }
        }
    }

    .menu-toggle {
        display: none;
        flex-direction: column;
        background: none;
        border: none;
        cursor: pointer;
        padding: 5px;
        flex-shrink: 0;

        span {
            width: 22px;
            height: 2px;
            background: #333;
            margin: 2px 0;
            transition: 0.3s;
        }
    }

    @media (max-width: 1024px) {
        .nav {
            .nav-list {
                gap: 20px;
            }
        }

        .header-actions {
            gap: 10px;

            .phone {
                font-size: 0.8rem;
            }

            .login-btn,
            .signup-btn {
                padding: 6px 12px;
                font-size: 0.8rem;
            }
        }
    }

    @media (max-width: 768px) {
        .header-content {
            height: 60px;
        }

        .nav {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: white;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            transform: translateY(-100%);
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
            justify-content: flex-start;

            &.nav-open {
                transform: translateY(0);
                opacity: 1;
                visibility: visible;
            }

            .nav-list {
                flex-direction: column;
                padding: 20px;
                gap: 15px;
                align-items: flex-start;
            }
        }

        .header-actions {
            .phone {
                display: none;
            }

            .login-btn,
            .signup-btn {
                padding: 5px 10px;
                font-size: 0.75rem;
            }
        }

        .menu-toggle {
            display: flex;
        }

        .logo {
            font-size: 1.1rem;

            .logo-image {
                height: 30px;
                margin-right: 8px;
            }
        }
    }

    @media (max-width: 480px) {
        .container {
            padding: 0 15px;
        }

        .header-actions {
            gap: 8px;

            .login-btn,
            .signup-btn {
                padding: 4px 8px;
                font-size: 0.7rem;
            }
        }

        .logo {
            font-size: 1rem;

            .logo-image {
                height: 28px;
                margin-right: 6px;
            }
        }
    }

    @media (max-width: 320px) {
        .header-actions {
            .login-btn {
                display: none;
            }

            .signup-btn {
                padding: 4px 6px;
                font-size: 0.65rem;
            }
        }
    }
}
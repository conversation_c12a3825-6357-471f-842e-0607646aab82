.payment-management-container {
    padding: 24px;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

button {
    background: #1976d2;
    color: #fff;
    border: none;
    border-radius: 4px;
    padding: 8px 16px;
    margin: 8px 0;
    cursor: pointer;
    font-weight: 500;
    transition: background 0.2s;
}

button:hover {
    background: #125ea2;
}

.modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.3);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.modal form {
    background: #fff;
    padding: 32px 24px;
    border-radius: 8px;
    min-width: 320px;
    box-shadow: 0 4px 24px rgba(0, 0, 0, 0.12);
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.modal h3 {
    margin-bottom: 8px;
    color: #1976d2;
}

label {
    font-weight: 500;
    margin-top: 8px;
}

input,
select {
    padding: 6px 10px;
    border: 1px solid #ccc;
    border-radius: 4px;
    font-size: 1rem;
}

.lab-request-detail {
    background: #f5f7fa;
    border-left: 4px solid #1976d2;
    padding: 10px 16px;
    margin: 8px 0;
    border-radius: 4px;
    font-size: 0.97rem;
}

@media (max-width: 600px) {
    .modal form {
        min-width: 90vw;
        padding: 16px 8px;
    }
}

.payment-detail-modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    padding: 20px;
}

.payment-detail-modal {
    background: #fff;
    border-radius: 12px;
    width: 100%;
    max-width: 600px;
    max-height: 90vh;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.payment-detail-modal__header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 24px 24px 0 24px;
    border-bottom: 1px solid #e0e0e0;
}

.payment-detail-modal__header h3 {
    margin: 0;
    color: #333;
    font-size: 22px;
    font-weight: 600;
}

.payment-detail-modal__content {
    flex: 1;
    overflow-y: auto;
    padding: 24px;
    scroll-behavior: smooth;
    -webkit-overflow-scrolling: touch;
}

.payment-detail-modal__info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
    gap: 16px;
}

.payment-detail-modal__info-item {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.payment-detail-modal__info-item label {
    font-weight: 600;
    color: #666;
    font-size: 14px;
}

.payment-detail-modal__info-item span,
.payment-detail-modal__info-item input,
.payment-detail-modal__info-item textarea {
    color: #333;
    font-size: 16px;
    padding: 8px 12px;
    background: #f8f9fa;
    border-radius: 6px;
    border: 1px solid #e0e0e0;
    resize: none;
}

.payment-detail-modal__footer {
    display: flex;
    justify-content: flex-end;
    gap: 8px;
    padding: 16px 24px 24px 24px;
}

.payment-detail-modal__close-btn {
    background: none;
    border: none;
    color: #666;
    cursor: pointer;
    padding: 8px;
    border-radius: 50%;
    transition: all 0.3s ease;
    font-size: 20px;
}
.payment-detail-modal__close-btn:hover {
    background: #f0f0f0;
    color: #333;
}
// Test trực tiếp API tạo revisit appointment
const API_BASE = 'http://localhost:8080';

async function testCreateRevisitAppointmentDirect() {
    try {
        // Test trực tiếp tạo revisit appointment với appointment_id đã hoàn thành
        const createRevisitData = {
            appointmentId: 2, // Appointment đã hoàn thành
            revisitDate: '2025-08-10',
            revisitNotes: 'Test gửi email - Tái khám kiểm tra sức khỏe'
        };

        console.log('Đang tạo revisit appointment với data:', createRevisitData);

        const createResponse = await fetch(`${API_BASE}/api/revisit-appointments`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(createRevisitData)
        });

        console.log('Response status:', createResponse.status);
        
        if (createResponse.ok) {
            const result = await createResponse.json();
            console.log('✅ Tạo revisit appointment thành công:', result);
            console.log('📧 Email đã được gửi');
        } else {
            const error = await createResponse.text();
            console.log('❌ Lỗi tạo revisit appointment:', error);
        }

    } catch (error) {
        console.log('❌ Lỗi:', error.message);
    }
}

// Chạy test
testCreateRevisitAppointmentDirect();

// Light mode (default)
:root {
    --bg-primary: #ffffff;
    --bg-secondary: #f8f9fa;
    --text-primary: #2c3e50;
    --text-secondary: #4a5568;
    --border-color: #e0e0e0;
    --shadow-color: rgba(0, 0, 0, 0.1);
    --hover-bg: #f0f7ff;
    --input-bg: #ffffff;
    --input-text: #000000;
    --input-border: #e0e0e0;
    --button-primary: #1976d2;
    --button-primary-hover: #1565c0;
    --button-secondary: #f8f9fa;
    --button-secondary-hover: #e9ecef;
    --status-active: #e6f4ea;
    --status-active-text: #1e7e34;
    --status-inactive: #fff3cd;
    --status-inactive-text: #856404;
    --status-suspended: #f8d7da;
    --status-suspended-text: #721c24;
}

// Dark mode
body.dark-mode {
    --bg-primary: #1a1a1a;
    --bg-secondary: #2d2d2d;
    --text-primary: #ffffff;
    --text-secondary: #a0aec0;
    --border-color: #404040;
    --shadow-color: rgba(0, 0, 0, 0.3);
    --hover-bg: #2c3e50;
    --input-bg: #2d2d2d;
    --input-text: #ffffff;
    --input-border: #404040;
    --button-primary: #2196f3;
    --button-primary-hover: #1976d2;
    --button-secondary: #404040;
    --button-secondary-hover: #505050;
    --status-active: #1b4332;
    --status-active-text: #4ade80;
    --status-inactive: #854d0e;
    --status-inactive-text: #fbbf24;
    --status-suspended: #7f1d1d;
    --status-suspended-text: #f87171;
}

// Apply theme variables
body {
    background-color: var(--bg-primary);
    color: var(--text-primary);
    transition: background-color 0.3s ease, color 0.3s ease;
}

// Common elements
input,
select,
textarea {
    background-color: var(--input-bg) !important;
    color: var(--input-text) !important;
    border-color: var(--input-border) !important;
}

button {
    &.primary {
        background-color: var(--button-primary);

        &:hover {
            background-color: var(--button-primary-hover);
        }
    }

    &.secondary {
        background-color: var(--button-secondary);

        &:hover {
            background-color: var(--button-secondary-hover);
        }
    }
}

// Status badges
.status-badge {
    &.active {
        background-color: var(--status-active);
        color: var(--status-active-text);
    }

    &.inactive {
        background-color: var(--status-inactive);
        color: var(--status-inactive-text);
    }

    &.suspended {
        background-color: var(--status-suspended);
        color: var(--status-suspended-text);
    }
}
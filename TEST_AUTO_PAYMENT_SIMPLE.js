// Simple test script to test auto payment creation
// Run with: node TEST_AUTO_PAYMENT_SIMPLE.js

const http = require('http');

async function testAutoPaymentCreation() {
    console.log('=== Testing Auto Payment Creation ===\n');
    
    // Test 1: Get appointments to see what's available
    console.log('1. Fetching appointments...');
    
    try {
        const appointments = await makeRequest('/api/appointments', 'GET');
        console.log(`Found ${appointments.length} appointments`);
        
        if (appointments.length > 0) {
            console.log('Sample appointments:');
            appointments.slice(0, 5).forEach(apt => {
                console.log(`  - ID: ${apt.id}, Status: ${apt.status}, Date: ${apt.appointmentDate}, Patient: ${apt.patientId}`);
            });
            
            // Find a CONFIRMED appointment
            const confirmedApt = appointments.find(apt => apt.status === 'CONFIRMED');
            if (confirmedApt) {
                console.log(`\n2. Found CONFIRMED appointment to test: ID ${confirmedApt.id}`);
                console.log(`   Testing check-in (Note: This will fail without proper authentication)`);
                
                // This will likely fail due to authentication, but we can see the error
                await testCheckIn(confirmedApt.id);
            } else {
                console.log('\n2. No CONFIRMED appointments found');
                
                // Check if there are CHECKED_IN appointments to see if auto-payment worked
                const checkedInApts = appointments.filter(apt => apt.status === 'CHECKED_IN');
                if (checkedInApts.length > 0) {
                    console.log(`\n   Found ${checkedInApts.length} CHECKED_IN appointments. Checking payments...`);
                    await checkPaymentsForAppointments(checkedInApts);
                }
            }
        }
        
        // Test 2: Check existing payments
        console.log('\n3. Checking existing payments...');
        await checkExistingPayments();
        
    } catch (error) {
        console.error('Error during test:', error.message);
    }
}

async function testCheckIn(appointmentId) {
    try {
        console.log(`\n   Attempting check-in for appointment ${appointmentId}...`);
        
        const result = await makeRequest(`/api/appointments/${appointmentId}/checkin`, 'POST', {});
        console.log('   ✅ Check-in successful!');
        console.log('   Updated appointment:', result);
        
        // Check if payment was created
        console.log('\n   Checking for auto-created payment...');
        setTimeout(async () => {
            await checkPaymentForAppointment(appointmentId);
        }, 1000);
        
    } catch (error) {
        console.log('   ❌ Check-in failed:', error.message);
        if (error.message.includes('401') || error.message.includes('403')) {
            console.log('   (This is expected - authentication required)');
        }
    }
}

async function checkPaymentsForAppointments(appointments) {
    for (const apt of appointments.slice(0, 3)) {
        console.log(`   Checking payment for appointment ${apt.id}...`);
        await checkPaymentForAppointment(apt.id);
    }
}

async function checkPaymentForAppointment(appointmentId) {
    try {
        // Try different payment endpoints
        const endpoints = [
            `/api/payments?appointmentId=${appointmentId}`,
            `/api/payments/appointment/${appointmentId}`,
            `/api/payments`
        ];
        
        for (const endpoint of endpoints) {
            try {
                const payments = await makeRequest(endpoint, 'GET');
                
                if (Array.isArray(payments)) {
                    const appointmentPayments = payments.filter(p => p.appointmentId === appointmentId);
                    if (appointmentPayments.length > 0) {
                        console.log(`     ✅ Found ${appointmentPayments.length} payment(s) for appointment ${appointmentId}:`);
                        appointmentPayments.forEach(payment => {
                            console.log(`       Payment ID: ${payment.id}, Amount: ${payment.amount}, Status: ${payment.status}`);
                            if (payment.notes && payment.notes.includes('tự động')) {
                                console.log(`       📝 Auto-created: ${payment.notes}`);
                            }
                        });
                        return;
                    }
                } else if (payments && payments.appointmentId === appointmentId) {
                    console.log(`     ✅ Found payment for appointment ${appointmentId}:`);
                    console.log(`       Payment ID: ${payments.id}, Amount: ${payments.amount}, Status: ${payments.status}`);
                    return;
                }
            } catch (e) {
                // Try next endpoint
            }
        }
        
        console.log(`     ❌ No payment found for appointment ${appointmentId}`);
        
    } catch (error) {
        console.log(`     ❌ Error checking payment: ${error.message}`);
    }
}

async function checkExistingPayments() {
    try {
        const payments = await makeRequest('/api/payments', 'GET');
        
        if (payments && payments.length > 0) {
            console.log(`   Found ${payments.length} total payments`);
            console.log('   Recent payments:');
            payments.slice(0, 3).forEach(payment => {
                console.log(`     - ID: ${payment.id}, Amount: ${payment.amount}, Status: ${payment.status}, Appointment: ${payment.appointmentId}`);
                if (payment.notes && payment.notes.includes('tự động')) {
                    console.log(`       📝 ${payment.notes}`);
                }
            });
        } else {
            console.log('   No payments found');
        }
        
    } catch (error) {
        console.log('   Could not fetch payments:', error.message);
    }
}

function makeRequest(path, method, data = null) {
    return new Promise((resolve, reject) => {
        const options = {
            hostname: 'localhost',
            port: 8080,
            path: path,
            method: method,
            headers: {
                'Content-Type': 'application/json'
            }
        };
        
        const req = http.request(options, (res) => {
            let responseData = '';
            
            res.on('data', (chunk) => {
                responseData += chunk;
            });
            
            res.on('end', () => {
                try {
                    if (res.statusCode >= 200 && res.statusCode < 300) {
                        const result = responseData ? JSON.parse(responseData) : {};
                        resolve(result);
                    } else {
                        reject(new Error(`HTTP ${res.statusCode}: ${responseData}`));
                    }
                } catch (e) {
                    reject(new Error(`Parse error: ${e.message}`));
                }
            });
        });
        
        req.on('error', (error) => {
            reject(error);
        });
        
        if (data) {
            req.write(JSON.stringify(data));
        }
        
        req.end();
    });
}

// Run the test
console.log('Starting auto payment creation test...');
console.log('Backend should be running on http://localhost:8080\n');

setTimeout(() => {
    testAutoPaymentCreation();
}, 1000);

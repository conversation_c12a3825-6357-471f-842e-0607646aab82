import api from './api';

const AppointmentService = {
    updateSubstituteDoctor: async (appointmentId, substituteDoctorId) => {
        const response = await api.patch(`/api/appointments/${appointmentId}/substitute`, { substituteDoctorId });
        return response.data;
    },
    updateStatus: async (appointmentId, status, notes = '') => {
        const response = await api.patch(`/api/appointments/${appointmentId}/status`, {
            status,
            notes
        });
        return response.data;
    },
};

export default AppointmentService;

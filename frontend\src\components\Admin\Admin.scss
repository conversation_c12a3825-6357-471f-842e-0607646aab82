.admin-page {
  min-height: 100vh;
  background: #f8f9fa;
  padding: 2rem 0;
  
  .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
  }
}

.admin-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 50px;
  margin-bottom: 2rem;
  
  h1 {
    color: #333;
    font-size: 2rem;
    font-weight: 600;
    margin: 0;
  }
  
  @media (max-width: 768px) {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }
}

.search-section {
  margin-bottom: 2rem;
  max-width: 400px;
}

.doctor-form-section {
  background: white;
  padding: 2rem;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  margin-bottom: 2rem;
  
  .form-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    
    h2 {
      
      color: #333;
      margin: 0;
      font-size: 1.5rem;
    }
    
    @media (max-width: 768px) {
      flex-direction: column;
      gap: 1rem;
      text-align: center;
    }
  }
  
  .doctor-form {
    .form-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      gap: 1rem;
      margin-bottom: 2rem;
      
      .form-select {
        width: 100%;
        padding: 0.75rem 1rem;
        border: 1px solid #ddd;
        border-radius: 6px;
        font-size: 1rem;
        background: white;
        
        &:focus {
          outline: none;
          border-color: #667eea;
          box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }
      }
    }
    
    .form-actions {
      text-align: right;
      
      @media (max-width: 768px) {
        text-align: center;
      }
    }
  }
}

.doctors-list {
  h2 {
    color: #333;
    margin-bottom: 1.5rem;
    font-size: 1.5rem;
  }
}

.doctors-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 1.5rem;
  
  @media (max-width: 768px) {
    grid-template-columns: 1fr;
  }
}

.doctor-card {
  background: white;
  padding: 1.5rem;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  }
  
  .doctor-info {
    margin-bottom: 1.5rem;
    
    h3 {
      color: #333;
      margin-bottom: 1rem;
      font-size: 1.2rem;
      font-weight: 600;
    }
    
    p {
      margin-bottom: 0.5rem;
      color: #666;
      font-size: 0.9rem;
      
      strong {
        color: #333;
      }
    }
    
    .status {
      padding: 0.25rem 0.5rem;
      border-radius: 12px;
      font-size: 0.8rem;
      font-weight: 500;
      margin-left: 0.5rem;
      
      &.active {
        background: #d4edda;
        color: #155724;
      }
      
      &.inactive {
        background: #f8d7da;
        color: #721c24;
      }
    }
  }
  
  .doctor-actions {
    display: flex;
    gap: 0.5rem;
    
    @media (max-width: 480px) {
      flex-direction: column;
    }
  }
}

.no-data {
  text-align: center;
  padding: 3rem;
  color: #666;
  font-style: italic;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

// Responsive Design
@media (max-width: 768px) {
  .admin-page {
    padding: 1rem 0;
    
    .container {
      padding: 0 1rem;
    }
  }
  
  .admin-header h1 {
    font-size: 1.5rem;
  }
  
  .doctor-form-section {
    padding: 1.5rem;
  }
  
  .doctors-grid {
    gap: 1rem;
  }
  
  .doctor-card {
    padding: 1rem;
  }
}

.admin-dashboard__container {
  display: flex;
  gap: 24px;
}

.admin-sidebar {
  margin-top: 50px;
  width: 260px;
  background: #fff;
  border-radius: 10px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.08);
  padding: 16px 0;
  display: flex;
  flex-direction: column;
  height: fit-content;
  min-width: 200px;
}

.admin-sidebar__tab {
  padding: 18px 28px;
  font-size: 1rem;
  color: #333;
  background: none;
  border: none;
  text-align: left;
  cursor: pointer;
  transition: background 0.2s, color 0.2s;
  border-right: 3px solid transparent;
  outline: none;
}
.admin-sidebar__tab.active {
  background: #e8f5e8;
  color: #2d5a2d;
  border-right: 3px solid #4caf50;
  font-weight: 600;
}
.admin-sidebar__tab:hover {
  background: #f8f9fa;
}

.admin-dashboard__content {
  flex: 1;
  min-width: 0;
}

.doctors-row {
  display: flex;
  gap: 1.5rem;
  overflow-x: auto;
  padding-bottom: 12px;
  scrollbar-width: thin;
  scrollbar-color: #c1c1c1 #f5f5f5;
}
.doctors-row::-webkit-scrollbar {
  height: 8px;
}
.doctors-row::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}
.doctor-card {
  min-width: 340px;
  max-width: 360px;
  flex: 0 0 auto;
}

@media (max-width: 900px) {
  .admin-dashboard__container {
    flex-direction: column;
  }
  .admin-sidebar {
    width: 100%;
    min-width: 0;
    flex-direction: row;
    border-radius: 8px;
    box-shadow: 0 1px 4px rgba(0,0,0,0.06);
    margin-bottom: 18px;
    padding: 0;
  }
  .admin-sidebar__tab {
    flex: 1;
    text-align: center;
    border-right: none;
    border-bottom: 3px solid transparent;
    padding: 14px 0;
  }
  .admin-sidebar__tab.active {
    border-bottom: 3px solid #4caf50;
    border-right: none;
  }
}

.doctors-table-wrapper {
  background: #fff;
  border-radius: 10px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.08);
  overflow-x: auto;
  margin-bottom: 2rem;
}
.doctors-table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
  min-width: 900px;
  background: #fff;
  border-radius: 10px;
  overflow: hidden;
}
.doctors-table th, .doctors-table td {
  padding: 12px 14px;
  text-align: left;
  font-size: 15px;
  border-bottom: 1px solid #f0f0f0;
  background: #fff;
}
.doctors-table th {
  font-weight: 600;
  color: #222;
  background: #fafbfc;
  border-bottom: 2px solid #e5e5e5;
}
.doctors-table tr:last-child td {
  border-bottom: none;
}
.doctors-table tr:hover {
  background: #f8f9fa;
}
.doctor-avatar {
  display: flex;
  align-items: center;
  justify-content: center;
}
.doctor-avatar img {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  object-fit: cover;
  border: 2px solid #e5e5e5;
}
.status.active {
  color: #27ae60;
  font-weight: 600;
}
.status.inactive {
  color: #e74c3c;
  font-weight: 600;
}
.doctor-actions {
  display: flex;
  gap: 6px;
}
.doctor-actions .btn {
  font-size: 13px;
  padding: 4px 14px;
  border-radius: 6px;
  min-width: unset;
}
@media (max-width: 1100px) {
  .doctors-table-wrapper {
    overflow-x: auto;
  }
  .doctors-table {
    min-width: 900px;
  }
}

.form-select--large {
  min-height: 48px;
  padding: 12px 16px;
  font-size: 1.08rem;
  border-radius: 8px;
  margin-bottom: 10px;
  text-align: left;
  direction: ltr;
}

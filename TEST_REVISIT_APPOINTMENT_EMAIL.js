// Test tạo revisit appointment và kiểm tra email
const API_BASE = 'http://localhost:8080';

async function testCreateRevisitAppointment() {
    try {
        // Lấy token đăng nhập bác sĩ
        const loginResponse = await fetch(`${API_BASE}/api/auth/login`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                email: '<EMAIL>',
                password: 'password123'
            })
        });

        if (!loginResponse.ok) {
            console.log('Lỗi đăng nhập:', loginResponse.status);
            return;
        }

        const loginData = await loginResponse.json();
        const token = loginData.token;
        console.log('Đăng nhập thành công');

        // Lấy danh sách appointments để tìm một appointment hoàn thành
        const appointmentsResponse = await fetch(`${API_BASE}/api/appointments/doctor`, {
            headers: {
                'Authorization': `Bearer ${token}`
            }
        });

        if (!appointmentsResponse.ok) {
            console.log('Lỗi lấy danh sách appointments:', appointmentsResponse.status);
            return;
        }

        const appointments = await appointmentsResponse.json();
        console.log('Số lượng appointments:', appointments.length);

        // Tìm appointment đã hoàn thành
        const completedAppointment = appointments.find(apt => apt.status === 'COMPLETED');
        if (!completedAppointment) {
            console.log('Không tìm thấy appointment đã hoàn thành');
            return;
        }

        console.log('Tìm thấy appointment hoàn thành:', completedAppointment.id);

        // Tạo revisit appointment
        const createRevisitData = {
            appointmentId: completedAppointment.id,
            revisitDate: '2025-08-10', // Ngày trong tương lai
            revisitNotes: 'Tái khám kiểm tra sức khỏe định kỳ - Test email'
        };

        const createResponse = await fetch(`${API_BASE}/api/revisit-appointments`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${token}`
            },
            body: JSON.stringify(createRevisitData)
        });

        if (createResponse.ok) {
            const result = await createResponse.json();
            console.log('✅ Tạo revisit appointment thành công:', result);
            console.log('📧 Email sẽ được gửi đến:', completedAppointment.patient?.email || 'patient email');
        } else {
            const error = await createResponse.text();
            console.log('❌ Lỗi tạo revisit appointment:', createResponse.status, error);
        }

    } catch (error) {
        console.log('❌ Lỗi:', error.message);
    }
}

// Chạy test
testCreateRevisitAppointment();

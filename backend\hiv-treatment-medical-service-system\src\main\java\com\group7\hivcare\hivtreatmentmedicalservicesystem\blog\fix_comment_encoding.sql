-- <PERSON><PERSON><PERSON> để sửa encoding cho bảng blog_comments
-- Chạy script này trong SQL Server Management Studio

-- 1. <PERSON><PERSON><PERSON> tra encoding hiện tại
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    CHARACTER_MAXIMUM_LENGTH,
    COLLATION_NAME
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_NAME = 'blog_comments' AND COLUMN_NAME = 'content';

-- 2. Sửa collation cho bảng blog_comments
ALTER TABLE blog_comments 
ALTER COLUMN content NVARCHAR(MAX) COLLATE SQL_Latin1_General_CP1_CI_AS;

-- 3. <PERSON><PERSON><PERSON> tra lại sau khi sửa
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    CHARACTER_MAXIMUM_LENGTH,
    COLLATION_NAME
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_NAME = 'blog_comments' AND COLUMN_NAME = 'content';

-- 4. <PERSON><PERSON><PERSON> dữ liệu cũ có thể bị lỗi encoding (tù<PERSON> chọn)
-- DELETE FROM blog_comments;

PRINT '<PERSON><PERSON> sửa encoding cho bảng blog_comments'; 
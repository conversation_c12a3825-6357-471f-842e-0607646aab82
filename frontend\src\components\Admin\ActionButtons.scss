// ActionButtons.scss - Style cho các nút hành động (xem, sửa, xóa, on/off)

// Nút action chung
.view-button,
.edit-button,
.delete-button,
.toggle-active-btn {
    width: 44px;
    height: 44px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    border: none;
    border-radius: 12px;
    margin: 0 4px;
    padding: 0;
    cursor: pointer;
    transition: background 0.18s, box-shadow 0.18s;
    vertical-align: middle;
}

// Nút xem
.view-button {
    background: #757575;
    color: #fff;
}

.view-button:hover {
    background: #616161;
}

// Nút sửa
.edit-button {
    background: #2196f3;
    color: #fff;
}

.edit-button:hover {
    background: #1565c0;
}

// Nút xóa
.delete-button {
    background: #f44336;
    color: #fff;
}

.delete-button:hover {
    background: #c62828;
}

// Nút on/off
.toggle-active-btn {
    background: #fff;
    color: #757575;
    font-size: 24px;
    border: 1.5px solid #e0e0e0;
}

.toggle-active-btn:hover {
    background: #f5f5f5;
}
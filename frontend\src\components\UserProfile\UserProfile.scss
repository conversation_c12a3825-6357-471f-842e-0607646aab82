.user-profile {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding: 100px 0 20px 0;

  &__container {
    margin: 0;
    display: flex;
    gap: 16px;
    padding-left: 10px;
    padding-right: 10px;
  }

  &__content {
    width: 100%;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    overflow: hidden;

  }
}

// Medication tab layout
.medication-tab-container {
  padding: 24px;
  
  .medication-section, .reminders-section {
    margin-bottom: 48px;
    
    &:last-child {
      margin-bottom: 0;
    }
  }
  
  .section-header {
    margin-bottom: 24px;
    padding-bottom: 16px;
    border-bottom: 2px solid #e8f4f8;
    
    h2 {
      margin: 0 0 8px 0;
      color: #2c3e50;
      font-size: 24px;
      font-weight: 600;
    }
    
    p {
      margin: 0;
      color: #6c757d;
      font-size: 14px;
      line-height: 1.5;
    }
  }
}

@media (max-width: 768px) {
  .user-profile {
    &__container {
      flex-direction: column;
      padding: 0 10px;
    }
  }
  
  .medication-tab-container {
    padding: 16px;
    
    .section-header {
      h2 {
        font-size: 20px;
      }
      
      p {
        font-size: 13px;
      }
    }
    
    .medication-section, .reminders-section {
      margin-bottom: 32px;
    }
  }
}
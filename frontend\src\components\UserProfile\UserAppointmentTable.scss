.appointment-status-table {
    padding: 1rem;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    margin-top: 20px;
}

.table-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 1rem;
}

.table-header h2 {
    margin: 0;
    font-size: 1.5rem;
}

.search-input {
    padding: 0.5rem 1rem;
    border: 1px solid #ccc;
    border-radius: 4px;
    font-size: 1rem;
    min-width: 260px;
    margin-left: 1rem;
}

.user-appointment-table table {
    width: 100%;
    border-collapse: collapse;
    background: #fff;
    border-radius: 8px;
    overflow: hidden;
    table-layout: fixed;
    /* Optional: Uncomment the next line if you want an outer border */
    /* border: 1px solid #e0e0e0; */
}

.user-appointment-table th,
.user-appointment-table td {
    padding: 0.75rem 1rem;
    border: none; /* Remove all column borders */
    text-align: left;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.user-appointment-table th {
    background: #f5f5f5;
}

.status-badge {
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-size: 0.875rem;
    font-weight: 500;
}
.status-badge.pending {
    background: #f2f2f2;
    color: #444;
    border: 1.5px solid #e0e0e0;
    font-weight: 500;
    box-shadow: none;
}

.status-badge.upcoming {

.appointment-status-table th,
.appointment-status-table td {
    padding: 4px 4px 4px 0;
    border-bottom: 1px solid #eee;
    font-size: 15px;
}

.appointment-status-table table {
    width: 100%;
    min-width: 900px;
    table-layout: fixed;
    border-collapse: separate;
    border-spacing: 0;
}

.appointment-status-table th {
    background: #fafbfc;
    font-weight: 600;
    text-align: left;
}
.appointment-status-table td {
    text-align: left;
    vertical-align: middle;
}
.appointment-status-table th:nth-child(5),
.appointment-status-table td:nth-child(5) {
    min-width: 110px;
    text-align: center;
}
.appointment-status-table th:nth-child(6),
.appointment-status-table td:nth-child(6) {
    min-width: 120px;
    text-align: center;
}
.appointment-status-table th:not(:last-child),
.appointment-status-table td:not(:last-child) {
    padding-right: 10px;
}

.appointment-status-table th:not(:last-child),
.appointment-status-table td:not(:last-child) {
    margin-right: 8px;
    padding-right: 12px;
}

.appointment-status-table th {
    background: #fafbfc;
    font-weight: 600;
    text-align: center;
}

.appointment-status-table td {
    text-align: center;
    vertical-align: middle;
}

.appointment-status-table th:nth-child(1),
.appointment-status-table td:nth-child(1) {
    min-width: 120px;
    text-align: left;
}
.appointment-status-table th:nth-child(2),
.appointment-status-table td:nth-child(2) {
    min-width: 150px;
    text-align: left;
}
.appointment-status-table th:nth-child(3),
.appointment-status-table td:nth-child(3) {
    min-width: 140px;
}
.appointment-status-table th:nth-child(4),
.appointment-status-table td:nth-child(4) {
    min-width: 140px;
    max-width: 200px;
    word-break: break-word;
    white-space: pre-line;
    text-align: left;
}
.appointment-status-table th:last-child,
.appointment-status-table td:last-child {
    padding-right: 24px;
    white-space: normal;
    word-break: break-word;
    max-width: 120px;
}
.appointment-status-table th:nth-child(5),
.appointment-status-table td:nth-child(5) {
    min-width: 110px;
}
.appointment-status-table th:nth-child(6),
.appointment-status-table td:nth-child(6) {
    min-width: 140px;
    max-width: 160px;
    text-align: center;
}

.appointment-status-table table {
    width: 100%;
    table-layout: fixed;
}

.appointment-status-table .cancel-button {
    min-width: 60px;
    padding: 6px 14px;
    border-radius: 6px;
    border: none;
    background: #f87171;
    color: #fff;
    font-weight: 500;
    cursor: pointer;
    transition: background 0.2s;
}

.appointment-status-table .cancel-button:hover {
    background: #dc2626;
}

.appointment-status-table .checkin-button {
    min-width: 90px;
    padding: 8px 16px;
    border-radius: 8px;
    border: none;
    background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
    color: #fff;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 14px;
    box-shadow: 0 2px 8px rgba(76, 175, 80, 0.3);
    position: relative;
    overflow: hidden;

    &::before {
        content: '';
        position: absolute;
        top: 50%;
        left: 50%;
        width: 0;
        height: 0;
        background: rgba(255, 255, 255, 0.3);
        border-radius: 50%;
        transform: translate(-50%, -50%);
        transition: width 0.6s, height 0.6s;
    }

    &:hover:not(:disabled) {
        background: linear-gradient(135deg, #45a049 0%, #3d8b40 100%);
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(76, 175, 80, 0.4);

        &::before {
            width: 300px;
            height: 300px;
        }
    }

    &:active:not(:disabled) {
        transform: translateY(0);
        box-shadow: 0 2px 8px rgba(76, 175, 80, 0.3);
    }

    &:disabled {
        opacity: 0.7;
        cursor: not-allowed;
        transform: none;
        box-shadow: 0 1px 4px rgba(76, 175, 80, 0.2);
    }
}

// Button "Cập nhật check-in" cho appointment đã check-in
.appointment-status-table .checkin-button.update-checkin {
    background: linear-gradient(135deg, #2196F3 0%, #1976D2 100%);
    box-shadow: 0 2px 8px rgba(33, 150, 243, 0.3);
    
    &:hover:not(:disabled) {
        background: linear-gradient(135deg, #1976D2 0%, #1565C0 100%);
        box-shadow: 0 4px 15px rgba(33, 150, 243, 0.4);
    }
    
    &:active:not(:disabled) {
        box-shadow: 0 2px 8px rgba(33, 150, 243, 0.3);
    }
    
    &:disabled {
        box-shadow: 0 1px 4px rgba(33, 150, 243, 0.2);
    }
}

.appointment-status-table td span[style*='italic'] {
    display: inline-block;
    width: 100%;
    text-align: center;
    color: #aaa;
    font-style: italic;
}
}

.status-badge.completed {
    background: #e8f5e8;
    color: #388e3c;
}

.status-badge.cancelled {
    background: #ffebee;
    color: #d32f2f;
}

.cancel-btn {
    background-color: #f44336;
    color: white;
    border: none;
    padding: 8px 12px;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.3s;

    &:hover {
        background-color: #d32f2f;
    }
}

.spinner {
    border: 4px solid rgba(0, 0, 0, 0.1);
    width: 24px;
    height: 24px;
    border-radius: 50%;
    border-left-color: #09f;
    margin: 0 auto;

    animation: spin 1s ease infinite;
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

.error-message {
    color: #d32f2f;
    background: #ffebee;
    padding: 0.75rem;
    border-radius: 4px;
    margin-bottom: 1rem;
}
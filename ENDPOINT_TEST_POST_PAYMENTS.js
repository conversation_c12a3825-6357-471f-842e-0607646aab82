// QUICK TEST: POST /api/payments endpoint
// Test xem endpoint mới có fix lỗi 500 không

const API_BASE = 'http://localhost:8080/api';

async function testNewPaymentEndpoint() {
    console.log('🧪 TESTING NEW PAYMENT ENDPOINT: POST /api/payments');
    console.log('=================================================');

    try {
        // Test với endpoint mới
        const testPaymentData = {
            patientId: 1,
            appointmentId: 1,
            labRequestId: 1,
            amount: 500000,
            method: "CASH",
            status: "PENDING"
        };

        // Test 1: POST without authentication (should get 401/403)
        console.log('\n1️⃣ Testing POST /api/payments without auth...');
        try {
            const noAuthResponse = await fetch(`${API_BASE}/payments`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(testPaymentData)
            });
            
            console.log('Status:', noAuthResponse.status);
            const noAuthResult = await noAuthResponse.text();
            console.log('Response:', noAuthResult.substring(0, 200));
            
            if (noAuthResponse.status === 401 || noAuthResponse.status === 403) {
                console.log('✅ Correctly rejected unauthenticated request');
            } else if (noAuthResponse.status === 500) {
                console.log('❌ Still getting 500 error - need to investigate');
            } else {
                console.log('⚠️ Unexpected status code');
            }
        } catch (error) {
            console.log('❌ Network error:', error.message);
        }

        // Test 2: Check if OPTIONS works (CORS preflight)
        console.log('\n2️⃣ Testing OPTIONS /api/payments (CORS preflight)...');
        try {
            const optionsResponse = await fetch(`${API_BASE}/payments`, {
                method: 'OPTIONS'
            });
            console.log('OPTIONS Status:', optionsResponse.status);
            if (optionsResponse.status === 200) {
                console.log('✅ OPTIONS request works - CORS configured');
            }
        } catch (error) {
            console.log('❌ OPTIONS error:', error.message);
        }

        // Test 3: Test GET endpoint (should work)
        console.log('\n3️⃣ Testing GET /api/payments (should also require auth)...');
        try {
            const getResponse = await fetch(`${API_BASE}/payments`);
            console.log('GET Status:', getResponse.status);
            if (getResponse.status === 401 || getResponse.status === 403) {
                console.log('✅ GET also correctly requires authentication');
            }
        } catch (error) {
            console.log('❌ GET error:', error.message);
        }

        console.log('\n📊 SUMMARY:');
        console.log('- POST /api/payments endpoint now exists');
        console.log('- Should return 401/403 for unauthorized requests');
        console.log('- Should return 200 with valid staff token for CASH payments');
        console.log('- Should return 200 with valid patient token for VNPAY payments');
        console.log('- No more 500 "method not supported" errors');

    } catch (error) {
        console.error('🚨 Test failed:', error.message);
    }
}

// Endpoint documentation
console.log(`
📋 NEW ENDPOINT DOCUMENTATION
============================

POST /api/payments
- Accepts both CASH and VNPAY payment methods
- Automatically routes to appropriate service based on method
- CASH: Requires STAFF/ADMIN role, calls createCashPayment()
- VNPAY: Allows PATIENT role, calls createVNPayPayment()
- Validates user role and payment method
- Returns appropriate PaymentDTO

This should fix the 500 error "method not supported" that frontend was getting.

Authentication Required:
- Bearer JWT token in Authorization header
- Role-based access control enforced

Example Request Body:
{
    "patientId": 1,
    "appointmentId": 1,
    "labRequestId": 1,
    "amount": 500000,
    "method": "CASH",
    "status": "PENDING"
}
`);

// Run the test
testNewPaymentEndpoint();

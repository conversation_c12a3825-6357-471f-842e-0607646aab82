.staff-payment-creation {
    padding: 24px;
    background-color: #f8f9fa;
    min-height: 100vh;

    .page-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 24px;
        padding: 20px;
        background: white;
        border-radius: 12px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

        h2 {
            margin: 0;
            color: #2d3748;
            font-size: 1.8rem;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 12px;

            svg {
                color: #48bb78;
            }
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            gap: 8px;

            &.btn-primary {
                background-color: #3182ce;
                color: white;

                &:hover {
                    background-color: #2c5aa0;
                    transform: translateY(-1px);
                }
            }
        }
    }

    .modal-overlay {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.5);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 1000;
        padding: 20px;
    }

    .modal-content {
        background: white;
        border-radius: 16px;
        box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
        width: 100%;
        max-width: 700px;
        max-height: 90vh;
        overflow-y: auto;

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 24px 32px;
            border-bottom: 1px solid #e2e8f0;

            h3 {
                margin: 0;
                color: #2d3748;
                font-size: 1.5rem;
                font-weight: 600;
            }

            .close-button {
                background: none;
                border: none;
                font-size: 1.5rem;
                color: #718096;
                cursor: pointer;
                padding: 8px;
                border-radius: 50%;
                transition: all 0.2s ease;

                &:hover {
                    background: #f7fafc;
                    color: #2d3748;
                }
            }
        }
    }

    .payment-form {
        padding: 32px;

        .form-group {
            margin-bottom: 24px;

            label {
                display: block;
                margin-bottom: 8px;
                font-weight: 500;
                color: #2d3748;
                font-size: 0.95rem;
            }

            input, select, textarea {
                width: 100%;
                padding: 12px 16px;
                border: 2px solid #e2e8f0;
                border-radius: 8px;
                font-size: 1rem;
                transition: all 0.2s ease;
                box-sizing: border-box;

                &:focus {
                    outline: none;
                    border-color: #3182ce;
                    box-shadow: 0 0 0 3px rgba(49, 130, 206, 0.1);
                }

                &:disabled {
                    background-color: #f7fafc;
                    cursor: not-allowed;
                }
            }

            textarea {
                resize: vertical;
                min-height: 80px;
            }
        }

        .type-selector {
            display: flex;
            gap: 12px;
            margin-top: 8px;

            .type-btn {
                flex: 1;
                padding: 16px 20px;
                border: 2px solid #e2e8f0;
                border-radius: 12px;
                background: white;
                color: #4a5568;
                font-weight: 500;
                cursor: pointer;
                transition: all 0.2s ease;
                display: flex;
                flex-direction: column;
                align-items: center;
                gap: 8px;

                svg {
                    font-size: 1.5rem;
                }

                &:hover {
                    border-color: #cbd5e0;
                    transform: translateY(-1px);
                }

                &.active {
                    border-color: #3182ce;
                    background: #ebf8ff;
                    color: #3182ce;
                }
            }
        }

        .selected-item-details {
            background: #f7fafc;
            border: 1px solid #e2e8f0;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 24px;

            h4 {
                margin: 0 0 16px 0;
                color: #2d3748;
                font-size: 1.1rem;
                font-weight: 600;
            }

            .details-content {
                p {
                    margin: 8px 0;
                    color: #4a5568;

                    strong {
                        color: #2d3748;
                    }
                }

                .lab-items {
                    margin-top: 12px;

                    ul {
                        margin: 8px 0 0 20px;
                        padding: 0;

                        li {
                            margin: 4px 0;
                            color: #4a5568;
                        }
                    }
                }
            }
        }

        .form-actions {
            display: flex;
            gap: 16px;
            justify-content: flex-end;
            margin-top: 32px;
            padding-top: 24px;
            border-top: 1px solid #e2e8f0;

            .btn {
                padding: 12px 32px;
                border: none;
                border-radius: 8px;
                font-weight: 500;
                cursor: pointer;
                transition: all 0.2s ease;
                min-width: 120px;

                &.btn-secondary {
                    background-color: #e2e8f0;
                    color: #4a5568;

                    &:hover {
                        background-color: #cbd5e0;
                    }
                }

                &.btn-primary {
                    background-color: #3182ce;
                    color: white;

                    &:hover:not(:disabled) {
                        background-color: #2c5aa0;
                        transform: translateY(-1px);
                    }

                    &:disabled {
                        background-color: #a0aec0;
                        cursor: not-allowed;
                        transform: none;
                    }
                }
            }
        }
    }
}

// Responsive design
@media (max-width: 768px) {
    .staff-payment-creation {
        padding: 16px;

        .page-header {
            flex-direction: column;
            gap: 16px;
            text-align: center;
        }

        .modal-content {
            margin: 20px;
            max-height: calc(100vh - 40px);
        }

        .payment-form {
            padding: 20px;

            .type-selector {
                flex-direction: column;

                .type-btn {
                    flex-direction: row;
                    justify-content: center;
                }
            }

            .form-actions {
                flex-direction: column;

                .btn {
                    width: 100%;
                }
            }
        }
    }
}

// Animation for modal
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: scale(0.9);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

.modal-content {
    animation: fadeIn 0.3s ease-out;
}

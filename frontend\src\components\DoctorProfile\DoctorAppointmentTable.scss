.appointment-status-table {
  padding: 1rem;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  margin-top: 20px;
}

.table-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 1rem;
}

.table-header h2 {
  margin: 0;
  font-size: 1.5rem;
}

.search-input {
  padding: 0.5rem 1rem;
  border: 1px solid #ccc;
  border-radius: 4px;
  font-size: 1rem;
  min-width: 260px;
  margin-left: 1rem;
}

.doctor-appointment-table table {
  width: 100%;
  border-collapse: collapse;
  background: #fff;
  border-radius: 8px;
  overflow: hidden;
  table-layout: fixed;
  /* Optional: Uncomment the next line if you want an outer border */
  /* border: 1px solid #e0e0e0; */
}

.doctor-appointment-table th,
.doctor-appointment-table td {
  padding: 0.75rem 1rem;
  border: none;
  /* Remove all column borders */
  text-align: left;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.appointment-status-table th {
  background: #f5f5f5;
}

.status-badge {
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.875rem;
  font-weight: 500;
}
.status-badge.pending {
  background: #f2f2f2;
  color: #444;
  border: 1.5px solid #e0e0e0;
  font-weight: 500;
}

.status-badge.upcoming {
  background: #e3f2fd;
  color: #1976d2;
}

.status-badge.ongoing {
  background: #fff3e0;
  color: #f57c00;
}

.status-badge.completed,
.status-badge.finished {
  background: #e8f5e8;
  color: #388e3c;
}

.status-badge.canceled {
  background: #ffebee;
  color: #d32f2f;
}

.view-detail-icon-btn {
  background: none;
  border: none;
  padding: 6px;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background 0.18s;
  color: #222;
  font-size: 18px;
}

.view-detail-icon-btn:hover {
  background: #e3f0fd;
}

.appointment-detail-modal {
  min-width: 350px;
  max-width: 500px;
  background: #fff;
  border-radius: 14px;
  box-shadow: 0 4px 32px rgba(0, 0, 0, 0.15);
  padding: 36px 32px 28px 32px;
  font-family: 'Segoe UI', 'Roboto', 'Arial', sans-serif;
}

.modal-title {
  text-align: center;
  font-size: 1.5rem;
  font-weight: 700;
  color: #374151;
  margin-bottom: 32px;
}

.appointment-detail-list {
  display: flex;
  flex-direction: column;
  gap: 18px;
}

.detail-row {
  display: flex;
  gap: 8px;
  font-size: 1.08rem;
  align-items: flex-start;
}

.detail-label {
  font-weight: 600;
  color: #374151;
  min-width: 140px;
  display: inline-block;
}

.detail-value {
  color: #222;
  word-break: break-word;
}

.close-modal-btn {
  margin-top: 12px;
  padding: 10px 32px;
  background: #1a73e8;
  color: #fff;
  border: none;
  border-radius: 8px;
  font-size: 1.08rem;
  font-weight: 500;
  cursor: pointer;
  transition: background 0.18s;
}

.close-modal-btn:hover {
  background: #1557b0;
}

.doctor-lab-request-view .modal-content {
  font-family: 'Segoe UI', 'Roboto', 'Arial', sans-serif;
  background: #fff;
  border-radius: 14px;
  box-shadow: 0 4px 32px rgba(0, 0, 0, 0.15);
  padding: 36px 32px 28px 32px;
  min-width: 350px;
  max-width: 500px;
}

.doctor-lab-request-view .modal-content h3 {
  text-align: center;
  font-size: 1.4rem;
  font-weight: 700;
  color: #374151;
  margin-bottom: 28px;
}

.doctor-lab-request-view .form-group {
  margin-bottom: 20px;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.doctor-lab-request-view label {
  font-weight: 600;
  color: #374151;
  margin-bottom: 8px;
  font-size: 1.05rem;
}

.doctor-lab-request-view select,
.doctor-lab-request-view textarea {
  width: 100%;
  padding: 10px 12px;
  border: 1.5px solid #bbb;
  border-radius: 6px;
  font-size: 1rem;
  margin-bottom: 0;
  margin-top: 0;
  font-family: inherit;
  box-sizing: border-box;
  transition: border-color 0.2s;
}

.doctor-lab-request-view select:focus,
.doctor-lab-request-view textarea:focus {
  outline: none;
  border-color: #1a73e8;
  box-shadow: 0 0 3px rgba(26, 115, 232, 0.15);
}

.doctor-lab-request-view .selected-test-types {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-top: 8px;
}

.selected-test-type-chip {
  background: #e3f0fd;
  color: #1976d2;
  border-radius: 16px;
  padding: 4px 12px;
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 0.98rem;
}

.selected-test-type-chip button {
  background: none;
  border: none;
  color: #1976d2;
  font-weight: bold;
  cursor: pointer;
  font-size: 16px;
  margin-left: 2px;
  padding: 0 2px;
  line-height: 1;
}

.doctor-lab-request-view .add-button {
  background: #1a73e8;
  color: #fff;
  border: none;
  border-radius: 6px;
  padding: 7px 18px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: background 0.18s;
  margin-left: 4px;
}

.doctor-lab-request-view .add-button:disabled {
  background: #bcd3f7;
  color: #fff;
  cursor: not-allowed;
}

.doctor-lab-request-view .add-button:hover:not(:disabled) {
  background: #1557b0;
}

.doctor-lab-request-view .save-button {
  background: #1a73e8;
  color: #fff;
  border: none;
  border-radius: 8px;
  padding: 10px 32px;
  font-size: 1.08rem;
  font-weight: 500;
  cursor: pointer;
  transition: background 0.18s;
}

.doctor-lab-request-view .save-button:disabled {
  background: #bcd3f7;
  color: #fff;
  cursor: not-allowed;
}

.doctor-lab-request-view .save-button:hover:not(:disabled) {
  background: #1557b0;
}

.doctor-lab-request-view .cancel-button {
  background: #f5f5f5;
  color: #333;
  border: none;
  border-radius: 8px;
  padding: 10px 32px;
  font-size: 1.08rem;
  font-weight: 500;
  cursor: pointer;
  margin-right: 8px;
  transition: background 0.18s;
}

.doctor-lab-request-view .cancel-button:hover {
  background: #e0e0e0;
}

// Status action buttons for appointment management
.status-action-btn {
  border: none;
  border-radius: 6px;
  padding: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.18s ease;
  margin: 0 2px;
  min-width: 32px;
  min-height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  
  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
  
  &.complete-btn {
    background: #e8f5e8;
    color: #2e7d32;
    border: 1px solid #c8e6c9;
    
    &:hover:not(:disabled) {
      background: #c8e6c9;
      border-color: #4caf50;
      transform: scale(1.05);
    }
  }
  
  &.no-show-btn {
    background: #fff3e0;
    color: #f57c00;
    border: 1px solid #ffcc80;
    
    &:hover:not(:disabled) {
      background: #ffcc80;
      border-color: #ff9800;
      transform: scale(1.05);
    }
  }
}
.terms-page {
    max-width: 800px;
    margin: 40px auto 60px auto;
    background: #fff;
    border-radius: 18px;
    box-shadow: 0 8px 32px rgba(52, 152, 219, 0.10);
    padding: 40px 32px 32px 32px;
    font-family: 'Segoe UI', <PERSON><PERSON>, sans-serif;
}

.terms-hero {
    text-align: center;
    margin-bottom: 36px;

    &__icon {
        font-size: 54px;
        color: #3498db;
        margin-bottom: 12px;
        animation: lock-bounce 1.2s infinite alternate;
    }

    h1 {
        font-size: 2.4rem;
        color: #217dbb;
        font-weight: 700;
        margin-bottom: 10px;
        letter-spacing: -1px;
    }

    p {
        color: #444;
        font-size: 1.15rem;
        margin-bottom: 0;
    }
}

@keyframes lock-bounce {
    0% {
        transform: translateY(0);
    }

    100% {
        transform: translateY(-8px);
    }
}

.terms-section {
    margin-bottom: 32px;
    background: #f8fafd;
    border-radius: 12px;
    padding: 28px 24px 18px 24px;
    box-shadow: 0 2px 8px rgba(52, 152, 219, 0.04);

    h2 {
        display: flex;
        align-items: center;
        gap: 10px;
        color: #3498db;
        font-size: 1.35rem;
        font-weight: 700;
        margin-bottom: 18px;
        letter-spacing: -0.5px;
    }

    p {
        color: #444;
        font-size: 1.08rem;
        margin-bottom: 12px;
        margin-top: 0;
        line-height: 1.7;
        text-align: left;
    }

    ul {
        padding-left: 0;
        list-style: none;
        margin: 0;

        li {
            display: flex;
            align-items: flex-start;
            gap: 12px;
            color: #222;
            font-size: 1.08rem;
            margin-bottom: 12px;
            line-height: 1.7;
            padding-left: 2px;
            text-align: left;

            svg {
                color: #27ae60;
                font-size: 1.25em;
                flex-shrink: 0;
                margin-top: 2px;
            }
        }
    }
}

.terms-section--contact {
    background: #eaf6fb;
    text-align: center;

    h2 {
        color: #217dbb;
    }

    a {
        color: #3498db;
        font-weight: 600;
        text-decoration: underline;

        &:hover {
            color: #217dbb;
        }
    }
}
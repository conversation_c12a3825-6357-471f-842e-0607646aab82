.patient-treatment-history {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;

    .history-header {
        text-align: center;
        margin-bottom: 30px;
        
        h2 {
            color: #2c3e50;
            font-size: 2rem;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
            
            svg {
                color: #3498db;
            }
        }
        
        p {
            color: #7f8c8d;
            font-size: 1.1rem;
        }
    }

    .history-tabs {
        display: flex;
        justify-content: center;
        margin-bottom: 30px;
        border-bottom: 1px solid #ecf0f1;
        
        .tab-button {
            background: none;
            border: none;
            padding: 15px 25px;
            font-size: 1rem;
            color: #7f8c8d;
            cursor: pointer;
            border-bottom: 3px solid transparent;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
            
            &:hover {
                color: #3498db;
                background: rgba(52, 152, 219, 0.1);
            }
            
            &.active {
                color: #3498db;
                border-bottom-color: #3498db;
                background: rgba(52, 152, 219, 0.1);
                font-weight: 600;
            }
            
            svg {
                font-size: 1.1rem;
            }
        }
    }

    .history-content {
        h3 {
            color: #2c3e50;
            margin-bottom: 25px;
            font-size: 1.4rem;
            border-left: 4px solid #3498db;
            padding-left: 15px;
        }
    }

    .no-history {
        text-align: center;
        padding: 60px 20px;
        color: #95a5a6;
        
        svg {
            font-size: 3rem;
            margin-bottom: 15px;
            opacity: 0.5;
        }
        
        p {
            font-size: 1.1rem;
            margin: 0;
        }
    }

    .history-list {
        position: relative;
    }

    .history-item {
        display: flex;
        margin-bottom: 25px;
        position: relative;
        
        .history-timeline {
            width: 30px;
            flex-shrink: 0;
            position: relative;
            margin-right: 20px;
            
            .timeline-dot {
                width: 16px;
                height: 16px;
                background: #3498db;
                border-radius: 50%;
                border: 3px solid white;
                box-shadow: 0 0 0 3px #3498db;
                position: relative;
                z-index: 2;
            }
            
            .timeline-line {
                position: absolute;
                left: 50%;
                top: 16px;
                bottom: -25px;
                width: 2px;
                background: #bdc3c7;
                transform: translateX(-50%);
                z-index: 1;
            }
        }
        
        .history-card {
            flex: 1;
            background: white;
            border-radius: 12px;
            padding: 25px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            border: 1px solid #ecf0f1;
            transition: all 0.3s ease;
            
            &:hover {
                box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
                transform: translateY(-2px);
            }
            
            .history-date {
                color: #7f8c8d;
                font-size: 0.9rem;
                margin-bottom: 15px;
                display: flex;
                align-items: center;
                gap: 8px;
                font-weight: 500;
                
                svg {
                    color: #3498db;
                }
            }
            
            .protocol-change {
                display: flex;
                align-items: center;
                gap: 15px;
                margin-bottom: 15px;
                flex-wrap: wrap;
                
                @media (max-width: 768px) {
                    flex-direction: column;
                    gap: 10px;
                }
                
                .protocol-from,
                .protocol-to {
                    flex: 1;
                    min-width: 200px;
                    
                    .label {
                        display: block;
                        font-size: 0.85rem;
                        color: #7f8c8d;
                        margin-bottom: 5px;
                        font-weight: 500;
                    }
                    
                    .value {
                        display: block;
                        font-weight: 600;
                        color: #2c3e50;
                        background: #f8f9fa;
                        padding: 8px 12px;
                        border-radius: 6px;
                        border-left: 3px solid #3498db;
                    }
                }
                
                .arrow-icon {
                    color: #e74c3c;
                    font-size: 1.2rem;
                    
                    @media (max-width: 768px) {
                        transform: rotate(90deg);
                    }
                }
            }
            
            .prescription-info {
                margin-bottom: 15px;
                
                .prescription-id {
                    .label {
                        display: inline-block;
                        font-size: 0.85rem;
                        color: #7f8c8d;
                        margin-right: 10px;
                        font-weight: 500;
                    }
                    
                    .value {
                        font-weight: 600;
                        color: #2c3e50;
                        background: #f8f9fa;
                        padding: 4px 8px;
                        border-radius: 4px;
                        font-family: monospace;
                    }
                }
            }

            .prescription-medications {
                margin-bottom: 15px;
                
                .label {
                    display: block;
                    font-size: 0.85rem;
                    color: #7f8c8d;
                    margin-bottom: 8px;
                    font-weight: 500;
                }
                
                .medications-list {
                    background: #f8f9fa;
                    border-radius: 8px;
                    padding: 12px;
                    border-left: 3px solid #27ae60;
                    
                    .medication-item {
                        display: flex;
                        flex-wrap: wrap;
                        gap: 10px;
                        padding: 8px 0;
                        border-bottom: 1px solid #ecf0f1;
                        
                        &:last-child {
                            border-bottom: none;
                        }
                        
                        .med-name {
                            font-weight: 600;
                            color: #2c3e50;
                            flex: 1;
                            min-width: 150px;
                        }
                        
                        .med-dosage {
                            background: #e8f5e8;
                            color: #27ae60;
                            padding: 3px 8px;
                            border-radius: 4px;
                            font-size: 0.85rem;
                            font-weight: 500;
                        }
                        
                        .med-frequency {
                            background: #e3f2fd;
                            color: #1976d2;
                            padding: 3px 8px;
                            border-radius: 4px;
                            font-size: 0.85rem;
                            font-weight: 500;
                        }
                        
                        .med-notes {
                            color: #7f8c8d;
                            font-style: italic;
                            font-size: 0.85rem;
                            flex: 1;
                            min-width: 100%;
                            margin-top: 5px;
                        }
                    }
                }
            }
            
            .change-reason,
            .history-notes,
            .treatment-period {
                margin-bottom: 12px;
                
                .label {
                    display: inline-block;
                    font-size: 0.85rem;
                    color: #7f8c8d;
                    margin-right: 10px;
                    font-weight: 500;
                    min-width: 100px;
                }
                
                .value {
                    color: #2c3e50;
                    font-weight: 500;
                }
            }
            
            .change-reason .value {
                background: #fff3cd;
                padding: 6px 10px;
                border-radius: 6px;
                border-left: 3px solid #ffc107;
                display: inline-block;
                margin-left: 5px;
            }
            
            .doctor-info {
                margin-bottom: 15px;
                
                .label {
                    display: inline-block;
                    font-size: 0.85rem;
                    color: #7f8c8d;
                    margin-right: 10px;
                    font-weight: 500;
                    min-width: 120px;
                    
                    svg {
                        color: #3498db;
                        margin-right: 5px;
                    }
                }
                
                .value {
                    color: #2c3e50;
                    font-weight: 600;
                    background: #e8f4fd;
                    padding: 6px 10px;
                    border-radius: 6px;
                    border-left: 3px solid #3498db;
                    display: inline-block;
                    margin-left: 5px;
                }
            }
        }
    }

    .treatment-history-loading,
    .treatment-history-error {
        text-align: center;
        padding: 60px 20px;
        font-size: 1.1rem;
    }
    
    .treatment-history-loading {
        color: #3498db;
    }
    
    .treatment-history-error {
        color: #e74c3c;
        background: #fdf2f2;
        border: 1px solid #f5c6cb;
        border-radius: 8px;
        margin: 20px 0;
    }
}

// Responsive design
@media (max-width: 768px) {
    .patient-treatment-history {
        padding: 15px;
        
        .history-header h2 {
            font-size: 1.5rem;
        }
        
        .history-tabs {
            flex-direction: column;
            
            .tab-button {
                width: 100%;
                justify-content: center;
                padding: 12px 20px;
            }
        }
        
        .history-item {
            .history-timeline {
                margin-right: 15px;
            }
            
            .history-card {
                padding: 20px;
            }
        }
    }
}

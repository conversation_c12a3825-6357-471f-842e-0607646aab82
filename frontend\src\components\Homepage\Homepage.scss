.homepage {
    main {
        padding-top: 70px; // <PERSON><PERSON> tr<PERSON>h bị header che
    }
}


// Services Section
.services-section {
    padding: 80px 0;
    background: #f8f9fa;

    .container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 0 20px;
    }

    .section-header {
        text-align: center;
        margin-bottom: 60px;

        h2 {
            font-size: 2.5rem;
            margin-bottom: 15px;
            color: #333;
        }

        p {
            font-size: 1.1rem;
            color: #666;
            max-width: 600px;
            margin: 0 auto;
        }
    }

    .services-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 30px;
    }

    .service-card {
        background: white;
        padding: 40px 30px;
        border-radius: 15px;
        text-align: center;
        box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
        transition: transform 0.3s ease;

        &:hover {
            transform: translateY(-5px);
        }

        .service-icon {
            font-size: 3rem;
            margin-bottom: 20px;
        }

        h3 {
            font-size: 1.3rem;
            margin-bottom: 15px;
            color: #333;
        }

        p {
            color: #666;
            line-height: 1.6;
            margin-bottom: 25px;
        }

        .service-btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 20px;
            cursor: pointer;
            transition: background 0.3s ease;

            &:hover {
                background: #5a6fd8;
            }
        }
    }
}

// Benefits Section
.benefits-section {
    padding: 80px 0;
    background: white;

    .container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 0 20px;
    }

    .section-header {
        text-align: center;
        margin-bottom: 60px;

        h2 {
            font-size: 2.5rem;
            margin-bottom: 15px;
            color: #333;
        }

        p {
            font-size: 1.1rem;
            color: #666;
            max-width: 600px;
            margin: 0 auto;
        }
    }

    .benefits-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
        gap: 30px;
    }

    .benefit-item {
        display: flex;
        align-items: flex-start;
        gap: 20px;

        .benefit-icon {
            background: #667eea;
            color: white;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            flex-shrink: 0;
        }

        .benefit-content {
            h3 {
                font-size: 1.2rem;
                margin-bottom: 10px;
                color: #333;
            }

            p {
                color: #666;
                line-height: 1.6;
            }
        }
    }
}

// Testimonials Section
.testimonials-section {
    padding: 80px 0;
    background: #f8f9fa;

    .container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 0 20px;
    }

    .section-header {
        text-align: center;
        margin-bottom: 60px;

        h2 {
            font-size: 2.5rem;
            margin-bottom: 15px;
            color: #333;
        }

        p {
            font-size: 1.1rem;
            color: #666;
        }
    }

    .testimonials-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 30px;
    }

    .testimonial-card {
        background: white;
        padding: 30px;
        border-radius: 15px;
        box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);

        .testimonial-content {
            margin-bottom: 20px;

            .stars {
                margin-bottom: 15px;
                font-size: 1.2rem;
            }

            p {
                color: #666;
                line-height: 1.6;
                font-style: italic;
            }
        }

        .testimonial-author {
            display: flex;
            align-items: center;
            gap: 15px;

            .author-avatar {
                width: 50px;
                height: 50px;
                background: #667eea;
                color: white;
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                font-weight: bold;
                font-size: 1.2rem;
            }

            .author-info {
                h4 {
                    margin: 0 0 5px 0;
                    color: #333;
                }

                span {
                    color: #666;
                    font-size: 0.9rem;
                }
            }
        }
    }
}

// CTA Section
.cta-section {
    padding: 80px 0;
    background: #2c3e50;
    color: white;
    text-align: center;

    .container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 0 20px;
    }

    .cta-content {
        h2 {
            font-size: 2.5rem;
            margin-bottom: 20px;
        }

        p {
            font-size: 1.1rem;
            margin-bottom: 40px;
            opacity: 0.9;
            max-width: 600px;
            margin-left: auto;
            margin-right: auto;
        }
    }

    .cta-buttons {
        display: flex;
        gap: 20px;
        justify-content: center;

        .btn-primary,
        .btn-secondary {
            padding: 15px 35px;
            border: none;
            border-radius: 25px;
            font-size: 1rem;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .btn-primary {
            background: #667eea;
            color: white;

            &:hover {
                background: #5a6fd8;
                transform: translateY(-2px);
            }
        }

        .btn-secondary {
            background: transparent;
            color: white;
            border: 2px solid white;

            &:hover {
                background: white;
                color: #2c3e50;
            }
        }
    }

    @media (max-width: 768px) {
        .cta-buttons {
            flex-direction: column;
            align-items: center;
        }
    }
}
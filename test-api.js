// Test API call để kiểm tra prescription medications
const axios = require('axios');

// Test patient ID = 1
const testPatientId = 1;
const token = 'your-token-here'; // Thay thế bằng token thực

async function testTreatmentPlansAPI() {
    try {
        console.log('Testing treatment plans API for patient ID:', testPatientId);
        
        const response = await axios.get(`http://localhost:8080/api/patient-treatment-plans/patient/${testPatientId}/active`, {
            headers: {
                'Authorization': `Bearer ${token}`
            }
        });
        
        console.log('Response data:', JSON.stringify(response.data, null, 2));
        
        if (response.data.length > 0) {
            const firstPlan = response.data[0];
            console.log('\nFirst treatment plan:');
            console.log('ID:', firstPlan.id);
            console.log('Prescription medications:', firstPlan.prescriptionMedicationDTOList);
        }
        
    } catch (error) {
        console.error('Error:', error.response ? error.response.data : error.message);
    }
}

// testTreatmentPlansAPI();
console.log('To test: Replace token and uncomment the function call');

.checkin-status-card {
    border-radius: 12px;
    padding: 20px;
    margin: 16px 0;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    border-left: 5px solid;

    &:hover {
        box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
        transform: translateY(-2px);
    }

    &.success {
        background: linear-gradient(135deg, #f0fff4 0%, #e6fffa 100%);
        border-left-color: #4CAF50;
    }

    &.warning {
        background: linear-gradient(135deg, #fffbf0 0%, #fef7e0 100%);
        border-left-color: #FF9800;
    }

    &.error {
        background: linear-gradient(135deg, #fff5f5 0%, #fed7d7 100%);
        border-left-color: #f44336;
    }

    &.info {
        background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
        border-left-color: #2196F3;
    }
}

.checkin-status-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 16px;
}

.appointment-info {
    flex: 1;

    h4 {
        margin: 0 0 8px 0;
        font-size: 18px;
        font-weight: 600;
        color: #333;
    }

    p {
        margin: 4px 0;
        color: #666;
        font-size: 14px;
    }

    .appointment-time {
        font-weight: 500;
        color: #2c3e50;
        font-size: 15px;
    }
}

.status-indicator {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
    font-weight: bold;
    color: white;

    &.success {
        background: #4CAF50;
    }

    &.warning {
        background: #FF9800;
    }

    &.error {
        background: #f44336;
    }

    &.info {
        background: #2196F3;
    }
}

.checkin-status-body {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.status-message {
    padding: 12px 16px;
    border-radius: 8px;
    
    strong {
        display: block;
        font-size: 16px;
        margin-bottom: 4px;
    }

    p {
        margin: 0;
        font-size: 14px;
        opacity: 0.8;
    }

    &.success {
        background: #e8f5e8;
        color: #2e7d32;
        border: 1px solid #c8e6c9;
    }

    &.warning {
        background: #fff3e0;
        color: #f57c00;
        border: 1px solid #ffcc02;
    }

    &.error {
        background: #ffebee;
        color: #c62828;
        border: 1px solid #ef9a9a;
    }

    &.info {
        background: #e3f2fd;
        color: #1565c0;
        border: 1px solid #90caf9;
    }
}

.checkin-action-button {
    align-self: flex-start;
    padding: 14px 28px;
    border: none;
    border-radius: 12px;
    background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
    color: white;
    font-weight: 700;
    font-size: 16px;
    cursor: pointer;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    display: flex;
    align-items: center;
    gap: 10px;
    min-height: 52px;
    box-shadow: 0 4px 20px rgba(76, 175, 80, 0.3);
    position: relative;
    overflow: hidden;
    text-transform: uppercase;
    letter-spacing: 0.5px;

    &::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
        transition: left 0.5s;
    }

    &:hover:not(:disabled) {
        background: linear-gradient(135deg, #45a049 0%, #3d8b40 100%);
        transform: translateY(-3px) scale(1.02);
        box-shadow: 0 8px 30px rgba(76, 175, 80, 0.4);

        &::before {
            left: 100%;
        }
    }

    &:active:not(:disabled) {
        transform: translateY(-1px) scale(1.01);
        transition: all 0.1s;
    }

    &:disabled {
        opacity: 0.7;
        cursor: not-allowed;
        transform: none;
        box-shadow: 0 2px 10px rgba(76, 175, 80, 0.2);

        &::before {
            display: none;
        }
    }

    // Pulse animation khi có thể check-in
    animation: pulse-glow 2s infinite;
}

@keyframes pulse-glow {
    0% {
        box-shadow: 0 4px 20px rgba(76, 175, 80, 0.3);
    }
    50% {
        box-shadow: 0 6px 25px rgba(76, 175, 80, 0.5);
    }
    100% {
        box-shadow: 0 4px 20px rgba(76, 175, 80, 0.3);
    }
}

.spinner-small {
    width: 16px;
    height: 16px;
    border: 2px solid transparent;
    border-top: 2px solid #ffffff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

// Responsive design
@media (max-width: 768px) {
    .checkin-status-card {
        margin: 12px 0;
        padding: 16px;
    }

    .checkin-status-header {
        flex-direction: column;
        gap: 12px;
    }

    .status-indicator {
        align-self: center;
    }

    .appointment-info {
        text-align: center;

        h4 {
            font-size: 16px;
        }
    }

    .checkin-action-button {
        align-self: stretch;
        justify-content: center;
    }
}

// Test endpoint không cần auth để kiểm tra email
const API_BASE = 'http://localhost:8080';

async function testEmailSending() {
    try {
        // Test trực tiếp endpoint test
        const createRevisitData = {
            appointmentId: 2, // Appointment đã hoàn thành có <NAME_EMAIL>
            revisitDate: '2025-08-10',
            revisitNotes: 'TEST EMAIL - Tái khám kiểm tra sức khỏe định kỳ'
        };

        console.log('🔥 Đang test gửi email với data:', createRevisitData);

        const response = await fetch(`${API_BASE}/api/test/revisit-appointment`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(createRevisitData)
        });

        console.log('📡 Response status:', response.status);
        
        if (response.ok) {
            const result = await response.json();
            console.log('✅ Tạo revisit appointment thành công:', result);
            console.log('📧 Kiểm tra log backend để xem email có được gửi');
            console.log('📧 Email sẽ được gửi đến: <EMAIL>');
        } else {
            const error = await response.text();
            console.log('❌ Lỗi:', error);
        }

    } catch (error) {
        console.log('❌ Network error:', error.message);
    }
}

// Chạy test
testEmailSending();

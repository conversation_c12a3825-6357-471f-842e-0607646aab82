.booking-wrapper {
    max-width: 960px;
    margin: 0 auto;
    padding: 40px 16px;
    background-color: #fff;

    h1 {
        font-size: 2rem;
        font-weight: bold;
        color: #000;
        margin-bottom: 0.2rem;
    }

    .subtitle {
        color: #555;
        margin-bottom: 2rem;
    }

    .booking-form {
        border: 1px solid #eee;
        padding: 30px;
        border-radius: 12px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);

        h3 {
            margin-bottom: 0.5rem;
        }

        .note {
            font-size: 0.95rem;
            color: #666;
            margin-bottom: 1.5rem;
        }

        form {
            display: flex;
            flex-direction: column;
            gap: 1.2rem;

            .form-group {
                display: flex;
                gap: 1rem;
                flex-wrap: wrap;

                .form-item {
                    flex: 1;
                    display: flex;
                    flex-direction: column;
                }
            }

            .form-item {
                display: flex;
                flex-direction: column;

                label {
                    font-weight: 500;
                    margin-bottom: 0.4rem;
                    color: #333;
                }

                input,
                select,
                textarea {
                    padding: 0.6rem 0.8rem;
                    border-radius: 8px;
                    border: 1px solid #ccc;
                    font-size: 1rem;
                }

                textarea {
                    resize: vertical;
                    min-height: 80px;
                }
            }

            .full-width {
                width: 100%;
            }

            .submit-btn {
                margin-top: 1rem;
                background-color: #000;
                color: #fff;
                padding: 0.9rem;
                font-size: 1.1rem;
                border: none;
                border-radius: 10px;
                cursor: pointer;
                transition: background 0.3s ease;

                &:hover {
                    background-color: #333;
                }
            }
        }
    }
}

@media (max-width: 768px) {
    .booking-form form .form-group {
        flex-direction: column;
    }
}
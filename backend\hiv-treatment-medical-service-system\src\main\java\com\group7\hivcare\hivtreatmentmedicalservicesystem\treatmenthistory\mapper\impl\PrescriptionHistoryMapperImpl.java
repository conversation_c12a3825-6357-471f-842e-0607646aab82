package com.group7.hivcare.hivtreatmentmedicalservicesystem.treatmenthistory.mapper.impl;

import com.group7.hivcare.hivtreatmentmedicalservicesystem.common.entity.PrescriptionHistory;
import com.group7.hivcare.hivtreatmentmedicalservicesystem.common.entity.PrescriptionMedication;
import com.group7.hivcare.hivtreatmentmedicalservicesystem.common.repository.ARVMedicationRepository;
import com.group7.hivcare.hivtreatmentmedicalservicesystem.common.repository.PrescriptionMedicationRepository;
import com.group7.hivcare.hivtreatmentmedicalservicesystem.treatmenthistory.dto.PrescriptionHistoryDTO;
import com.group7.hivcare.hivtreatmentmedicalservicesystem.treatmenthistory.mapper.PrescriptionHistoryMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

@Component
public class PrescriptionHistoryMapperImpl implements PrescriptionHistoryMapper {
    
    @Autowired
    private PrescriptionMedicationRepository prescriptionMedicationRepository;
    
    @Autowired
    private ARVMedicationRepository arvMedicationRepository;
    
    @Override
    public PrescriptionHistoryDTO toDTO(PrescriptionHistory entity) {
        if (entity == null) {
            return null;
        }
        
        // Lấy thông tin thuốc từ đơn thuốc cũ
        List<PrescriptionHistoryDTO.MedicationInfo> oldMedications = 
            prescriptionMedicationRepository.findByPrescriptionId(entity.getPrescription().getId())
                .stream()
                .map(this::convertToMedicationInfo)
                .collect(Collectors.toList());
        
        return PrescriptionHistoryDTO.builder()
                .id(entity.getId())
                .treatmentPlanId(entity.getTreatmentPlan().getId())
                .prescriptionId(entity.getPrescription().getId())
                .changeReason(entity.getChangeReason())
                .createdAt(entity.getCreatedAt())
                .patientId(entity.getTreatmentPlan().getPatient().getId())
                .patientName(entity.getTreatmentPlan().getPatient().getUser().getFullName())
                .doctorId(entity.getTreatmentPlan().getDoctor().getId())
                .doctorName(entity.getTreatmentPlan().getDoctor().getUser().getFullName())
                .oldMedications(oldMedications)
                .build();
    }
    
    private PrescriptionHistoryDTO.MedicationInfo convertToMedicationInfo(PrescriptionMedication prescriptionMedication) {
        String medicationName = arvMedicationRepository.findById(prescriptionMedication.getId().getMedicationId())
                .map(medication -> medication.getName())
                .orElse("Không xác định");
        
        return PrescriptionHistoryDTO.MedicationInfo.builder()
                .medicationId(prescriptionMedication.getId().getMedicationId())
                .name(medicationName)
                .dosage(prescriptionMedication.getDosage())
                .frequency(prescriptionMedication.getFrequency())
                .durationDays(prescriptionMedication.getDurationDays())
                .notes(prescriptionMedication.getNotes())
                .build();
    }
    
    @Override
    public List<PrescriptionHistoryDTO> toDTOList(List<PrescriptionHistory> entities) {
        if (entities == null) {
            return null;
        }
        return entities.stream()
                .map(this::toDTO)
                .collect(Collectors.toList());
    }
} 
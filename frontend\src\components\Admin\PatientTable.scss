.patient-table {
    padding: 1rem;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    margin-top: 20px;
}

.table-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 1rem;
    flex-wrap: wrap;
    gap: 1rem;
}

.table-header h2 {
    margin: 0;
    font-size: 1.5rem;
    color: #333;
}

.search-input {
    padding: 0.5rem 1rem;
    border: 1px solid #ccc;
    border-radius: 4px;
    font-size: 1rem;
    min-width: 260px;
}

.add-button {
    background: #4CAF50;
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 4px;
    cursor: pointer;
    font-size: 1rem;
    font-weight: 500;
    transition: background-color 0.3s;
}

.add-button:hover {
    background: #45a049;
}

.patient-table table {
    width: 100%;
    border-collapse: collapse;
    background: #fff;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    border-radius: 8px;
    overflow: hidden;
    table-layout: fixed;
}

.patient-table th,
.patient-table td {
    padding: 0.75rem 1rem;
    border: none;
    text-align: left;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.patient-table th {
    background: #f5f5f5;
    font-weight: 600;
    color: #333;
    cursor: pointer;
    user-select: none;
}

.patient-table th:hover {
    background: #e9ecef;
}

.patient-table tr:hover {
    background-color: #f9f9f9;
}

.edit-button,
.toggle-active-btn {
    padding: 0.4rem 0.8rem;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.875rem;
    margin-right: 0.5rem;
    transition: background-color 0.3s;
}

.edit-button {
    background: #2196F3;
    color: white;
}

.edit-button:hover {
    background: #1976D2;
}

.toggle-active-btn {
    background: transparent;
    color: #4caf50;
}

.toggle-active-btn:hover {
    background: #f5f5f5;
}

.loading {
    text-align: center;
    padding: 2rem;
    color: #666;
}

.error-message {
    background: #ffebee;
    color: #c62828;
    padding: 1rem;
    border-radius: 4px;
    margin-bottom: 1rem;
}
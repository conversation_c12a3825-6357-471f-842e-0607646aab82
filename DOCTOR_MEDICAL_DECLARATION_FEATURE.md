# Tính năng: <PERSON><PERSON><PERSON> sĩ xem thông tin khai báo y tế bệnh nhân

## 📋 Mô tả tính năng

Cho phép bác sĩ xem thông tin khai báo y tế của bệnh nhân khi xem chi tiết, bao gồm:
- Tình trạng mang thai
- <PERSON><PERSON><PERSON> chứng hiện tại  
- <PERSON>hi chú sức khỏe
- Thuốc đang sử dụng
- Tiền sử dị ứng
- Thông tin liên hệ khẩn cấp

## 🔧 Thay đổi Backend

### 1. AppointmentDeclarationController.java
**Thêm authorization cho các endpoints:**

```java
// Thêm import
import org.springframework.security.access.prepost.PreAuthorize;

// Cập nhật endpoints
@GetMapping("/appointment/{appointmentId}")
@PreAuthorize("hasAnyRole('PATIENT', 'DOCTOR', 'STAFF', 'ADMIN')")
public ResponseEntity<AppointmentDeclarationDTO> getByAppointmentId(@PathVariable Integer appointmentId)

@GetMapping("/patient/{patientId}")
@PreAuthorize("hasAnyRole('PATIENT', 'DOCTOR', 'STAFF', 'ADMIN')")
public ResponseEntity<List<AppointmentDeclarationDTO>> getByPatientId(@PathVariable Integer patientId)

@GetMapping("/doctor/{doctorId}")
@PreAuthorize("hasAnyRole('DOCTOR', 'STAFF', 'ADMIN')")
public ResponseEntity<List<AppointmentDeclarationDTO>> getByDoctorId(@PathVariable Integer doctorId)
```

**Lợi ích:**
- Bác sĩ có quyền xem khai báo y tế của bệnh nhân
- Bảo mật thông tin theo đúng quyền hạn của từng role
- API có sẵn để frontend sử dụng

## 🎨 Thay đổi Frontend

### 1. PatientMedicalDeclaration.jsx (Mới)
**Component hiển thị thông tin khai báo y tế:**

```jsx
const PatientMedicalDeclaration = ({ appointmentId, patientId, appointmentDate }) => {
    // Tự động lấy khai báo theo appointmentId hoặc patientId
    // Hiển thị đầy đủ thông tin với UI đẹp mắt
}
```

**Features:**
- ✅ Loading và error states
- ✅ Empty state khi không có khai báo
- ✅ Color coding cho tình trạng mang thai
- ✅ Special styling cho dị ứng (nền vàng cảnh báo)
- ✅ Icons cho từng loại thông tin
- ✅ Responsive design

### 2. PatientMedicalDeclaration.scss (Mới)
**Styling chi tiết:**

```scss
.medical-declaration {
    // Main container styling
    
    &__header {
        // Header với icon và title
    }
    
    &__content {
        // Content area với các declaration items
    }
}

.declaration-item {
    // Individual declaration item styling
    // Color coding dựa trên loại thông tin
    // Special handling cho allergies, pregnancy status
}
```

### 3. PatientDetailModal.jsx (Cập nhật)
**Tích hợp component mới:**

```jsx
// Thêm import
import PatientMedicalDeclaration from './PatientMedicalDeclaration';

// Thêm vào modal content
<div className="patient-detail-modal__section">
    <PatientMedicalDeclaration 
        patientId={patient.id}
        appointmentId={patient.appointments?.[0]?.id}
    />
</div>
```

### 4. AppointmentDeclarationService.js (Sử dụng)
**Service đã có sẵn với các methods:**
- `getDeclarationByAppointmentId(appointmentId)`
- `getDeclarationsByPatientId(patientId)`
- `getDeclarationsByDoctorId(doctorId)`

## 📊 Cấu trúc dữ liệu

### AppointmentDeclarationDTO
```json
{
  "id": 1,
  "appointmentId": 2,
  "isPregnant": false,
  "symptoms": "Mệt mỏi, đau đầu nhẹ",
  "healthNotes": "Bệnh nhân có tiền sử cao huyết áp",
  "currentMedications": "Paracetamol 500mg x2/ngày",
  "allergies": "Dị ứng với Penicillin",
  "emergencyContact": "Nguyễn Văn A (Chồng)",
  "emergencyPhone": "**********",
  "createdAt": "2025-08-04T10:30:00"
}
```

## 🔐 Quyền truy cập

| Role | Xem khai báo của bệnh nhân | Xem khai báo theo appointment | Xem khai báo theo doctor |
|------|---------------------------|-------------------------------|-------------------------|
| PATIENT | ✅ (của chính mình) | ✅ (của chính mình) | ❌ |
| DOCTOR | ✅ (bệnh nhân của mình) | ✅ | ✅ (của chính mình) |
| STAFF | ✅ | ✅ | ✅ |
| ADMIN | ✅ | ✅ | ✅ |

## 🎯 User Flow

1. **Bác sĩ mở PatientDetailModal**
   - Nhấn "Xem chi tiết" trong PatientList
   
2. **Hiển thị thông tin bệnh nhân**
   - Thông tin cá nhân
   - **→ Thông tin khai báo y tế (MỚI)**
   - Tiến độ tuân thủ
   - Kết quả xét nghiệm
   - ...

3. **Xem thông tin khai báo**
   - Tự động load khai báo của appointment gần nhất
   - Hiển thị đầy đủ thông tin với UI trực quan
   - Các thông tin quan trọng được highlight

## 🚀 Triển khai

### Build và chạy:
```bash
# Backend
cd backend/hiv-treatment-medical-service-system
mvn clean compile
mvn spring-boot:run

# Frontend  
cd frontend
npm run dev
```

### Test tính năng:
1. Login với role DOCTOR
2. Vào trang Doctor Profile
3. Nhấn "Xem chi tiết" bất kỳ bệnh nhân nào
4. Kiểm tra section "Thông tin khai báo y tế"

## 🔍 Test Cases

### Test 1: Có khai báo y tế
- **Input:** Bệnh nhân có appointment với khai báo
- **Expected:** Hiển thị đầy đủ thông tin khai báo với UI đẹp

### Test 2: Không có khai báo
- **Input:** Bệnh nhân chưa có khai báo y tế
- **Expected:** Hiển thị thông báo "Bệnh nhân chưa có khai báo y tế"

### Test 3: Lỗi network
- **Input:** API không phản hồi
- **Expected:** Hiển thị thông báo lỗi

### Test 4: Authorization
- **Input:** Role không có quyền
- **Expected:** API trả về 403 Forbidden

## 📈 Lợi ích

### Cho bác sĩ:
- ✅ Xem nhanh thông tin sức khỏe quan trọng
- ✅ Nhận biết dị ứng để tránh kê đơn sai
- ✅ Biết tình trạng mang thai để điều trị phù hợp
- ✅ Có thông tin liên hệ khẩn cấp khi cần

### Cho hệ thống:
- ✅ Tận dụng dữ liệu có sẵn
- ✅ Cải thiện workflow của bác sĩ
- ✅ Bảo mật thông tin theo quyền hạn
- ✅ UI/UX trực quan, dễ sử dụng

## 🔄 Tương lai

### Có thể mở rộng:
- Thêm history của các khai báo
- Filter khai báo theo ngày
- Export thông tin khai báo
- Cảnh báo tự động khi có dị ứng
- Integration với hệ thống cảnh báo

.hero-section {
    background: linear-gradient(135deg, #eef1f9, #d6dcf5);
    padding: 100px 0;

    .container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 0 20px;
    }

    .hero-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 60px;
        align-items: center;
    }

    .hero-text {
        h1 {
            font-size: 3rem;
            font-weight: 700;
            color: #2c3e50;
            margin-bottom: 20px;
            line-height: 1.3;
        }

        p {
            font-size: 1.1rem;
            color: #4f4f4f;
            margin-bottom: 30px;
            line-height: 1.6;
        }

        .hero-actions {
            display: flex;
            gap: 20px;
            flex-wrap: wrap;
            margin-left: 90px;

            .btn {
                padding: 14px 30px;
                font-size: 1rem;
                border-radius: 30px;
                cursor: pointer;
                transition: all 0.3s ease;

                &.btn-primary {
                    background-color: #5b6def;
                    color: #fff;
                    border: none;

                    &:hover {
                        background-color: #475be6;
                        transform: translateY(-2px);
                        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
                    }
                }

                &.btn-outline {
                    background: transparent;
                    color: #000107;
                    border: 2px solid #000103;

                    &:hover {
                        background-color: #000104;
                        color: #fff;
                    }
                }
            }
        }
    }

    .hero-media {
        border-radius: 20px;
        overflow: hidden;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);

        .video {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
    }

    @media (max-width: 768px) {
        .hero-text h1 {
            font-size: 2rem;
        }

        .hero-actions {
            justify-content: center;
        }
    }
}
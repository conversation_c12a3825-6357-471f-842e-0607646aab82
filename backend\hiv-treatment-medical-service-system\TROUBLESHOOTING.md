# Khắc phục lỗi Blog Interactions

## Lỗi 500 khi like/unlike/comment

### Nguyên nhân:
- Database chưa có bảng `blog_likes` và `blog_comments`

### Cách khắc phục:

#### Cách 1: Chạy migration (khuyến nghị)
```bash
cd backend/hiv-treatment-medical-service-system
mvn flyway:migrate
```

#### Cách 2: Tạo bảng thủ công
1. Mở SQL Server Management Studio
2. Kết nối đến database `hiv_treatment_db`
3. Chạy file `create_blog_tables.sql`

#### Cách 3: Chạy script tự động
```bash
cd backend/hiv-treatment-medical-service-system
run_sql.bat
```

### Kiểm tra:
Sau khi tạo bảng, restart backend:
```bash
mvn spring-boot:run
```

### Test:
1. Đăng nhập vào hệ thống
2. <PERSON><PERSON>o trang blog detail
3. Thử like/unlike và comment
4. <PERSON><PERSON><PERSON> tra console backend để xem log

## Lỗi encoding khi comment

### Triệu chứng:
- Comment hiển thị các ký tự lạ như `?`, ``, hoặc mã hóa
- Emoji hiển thị sai
- Tiếng Việt bị lỗi

### Giải pháp:

#### Bước 1: Sửa encoding database
1. Mở SQL Server Management Studio
2. Kết nối đến database `hiv_treatment_db`
3. Chạy file `fix_comment_encoding.sql`

#### Bước 2: Restart backend
```bash
mvn spring-boot:run
```

#### Bước 3: Xóa comment cũ (tùy chọn)
```sql
DELETE FROM blog_comments;
```

#### Bước 4: Test comment mới
- Thử comment với emoji: 😊👍❤️
- Thử comment tiếng Việt: "Bài viết rất hay!"
- Thử comment dài với nhiều ký tự đặc biệt

### Debug:
Kiểm tra log backend khi comment để xem encoding:
```
=== ADD COMMENT CONTROLLER DEBUG ===
Received content: ...
Decoded content: ...
``` 
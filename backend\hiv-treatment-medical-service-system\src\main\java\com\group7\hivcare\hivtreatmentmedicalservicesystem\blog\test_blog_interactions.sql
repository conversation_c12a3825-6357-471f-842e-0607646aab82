-- Test script để kiểm tra blog interactions
-- Chạy script này trong SQL Server Management Studio

-- 1. <PERSON><PERSON><PERSON> tra bảng blog_likes
SELECT 
    'blog_likes' as table_name,
    COUNT(*) as total_records
FROM blog_likes;

-- 2. <PERSON><PERSON><PERSON> tra bảng blog_comments  
SELECT 
    'blog_comments' as table_name,
    COUNT(*) as total_records
FROM blog_comments;

-- 3. <PERSON>ểm tra like count cho blog cụ thể (thay blog_id = 1)
SELECT 
    b.id as blog_id,
    b.title as blog_title,
    COUNT(bl.id) as like_count
FROM blogs b
LEFT JOIN blog_likes bl ON b.id = bl.blog_id
WHERE b.id = 1
GROUP BY b.id, b.title;

-- 4. <PERSON><PERSON>m tra comment count cho blog cụ thể (thay blog_id = 1)
SELECT 
    b.id as blog_id,
    b.title as blog_title,
    COUNT(bc.id) as comment_count
FROM blogs b
LEFT JOIN blog_comments bc ON b.id = bc.blog_id
WHERE b.id = 1
GROUP BY b.id, b.title;

-- 5. <PERSON><PERSON><PERSON> tra user đã like blog chưa (thay blog_id = 1, user_id = 1)
SELECT 
    b.id as blog_id,
    b.title as blog_title,
    u.id as user_id,
    u.email as user_email,
    CASE WHEN bl.id IS NOT NULL THEN 'YES' ELSE 'NO' END as is_liked
FROM blogs b
CROSS JOIN users u
LEFT JOIN blog_likes bl ON b.id = bl.blog_id AND u.id = bl.user_id
WHERE b.id = 1 AND u.id = 1;

-- 6. Hiển thị tất cả likes cho blog
SELECT 
    bl.id as like_id,
    b.title as blog_title,
    u.email as user_email,
    u.full_name as user_name,
    bl.created_at
FROM blog_likes bl
JOIN blogs b ON bl.blog_id = b.id
JOIN users u ON bl.user_id = u.id
WHERE b.id = 1
ORDER BY bl.created_at DESC;

-- 7. Hiển thị tất cả comments cho blog
SELECT 
    bc.id as comment_id,
    b.title as blog_title,
    u.email as user_email,
    u.full_name as user_name,
    bc.content as comment_content,
    bc.created_at
FROM blog_comments bc
JOIN blogs b ON bc.blog_id = b.id
JOIN users u ON bc.user_id = u.id
WHERE b.id = 1
ORDER BY bc.created_at DESC;

PRINT 'Test script completed!'; 
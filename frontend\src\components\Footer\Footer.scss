.footer {
    background: #f8f9fa;
    color: #333;
    padding: 50px 0 20px;
    border-top: 1px solid #e9ecef;

    .container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 0 20px;
    }

    .footer-content {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 40px;
        margin-bottom: 40px;
    }

    .footer-section {
        .logo {
            display: flex;
            align-items: center;
            font-size: 1.2rem;
            font-weight: 600;
            margin-bottom: 15px;
            color: #333;

            .logo-icon {
                margin-right: 8px;
                font-size: 1.4rem;
            }
        }

        h3 {
            margin-bottom: 20px;
            color: #333;
            font-size: 1rem;
            font-weight: 600;
        }

        p {
            color: #666;
            line-height: 1.6;
            font-size: 0.9rem;
        }

        ul {
            list-style: none;
            padding: 0;

            li {
                margin-bottom: 8px;

                a {
                    color: #666;
                    text-decoration: none;
                    font-size: 0.9rem;
                    transition: color 0.3s ease;

                    &:hover {
                        color: #007bff;
                    }
                }
            }
        }

        .contact-info {
            font-size: 16px;
            line-height: 1.8;

            .icon {
                margin-right: 8px;
                color: #2E86C1;
            }

            a {
                color: inherit;
                text-decoration: none;

                &:hover {
                    text-decoration: underline;
                }
            }
        }
    }

    .footer-bottom {
        border-top: 1px solid #e9ecef;
        padding-top: 20px;
        display: flex;
        justify-content: space-between;
        align-items: center;

        .footer-chat {
            .chat-widget {
                display: flex;
                align-items: center;
                background: #333;
                color: white;
                padding: 10px 15px;
                border-radius: 6px;
                cursor: pointer;
                font-size: 0.9rem;

                span {
                    font-size: 1.1rem;
                    margin-right: 8px;
                }

                .chat-content {
                    p {
                        margin: 0;
                        font-size: 0.9rem;
                        font-weight: 500;
                        color: white;
                    }

                    small {
                        font-size: 0.75rem;
                        opacity: 0.8;
                        color: white;
                    }
                }
            }
        }

        .copyright {
            color: #666;
            font-size: 0.85rem;
        }
    }

    @media (max-width: 768px) {
        .footer-bottom {
            flex-direction: column;
            gap: 20px;
            text-align: center;
        }
    }
}
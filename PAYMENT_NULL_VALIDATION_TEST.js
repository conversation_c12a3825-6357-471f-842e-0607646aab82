// Test payment creation with null validation fixes
// This test verifies that the payment system handles null IDs properly and provides clear error messages

const BASE_URL = 'http://localhost:8080';

// Test data for payment creation
const testPaymentData = {
    valid: {
        appointmentId: 1,
        patientId: 1,
        staffId: 1,
        method: "CASH",
        amount: 100000.0,
        paymentDate: "2025-08-04T08:00:00"
    },
    nullAppointmentId: {
        appointmentId: null,
        patientId: 1,
        staffId: 1,
        method: "CASH",
        amount: 100000.0,
        paymentDate: "2025-08-04T08:00:00"
    },
    nullPatientId: {
        appointmentId: 1,
        patientId: null,
        staffId: 1,
        method: "CASH",
        amount: 100000.0,
        paymentDate: "2025-08-04T08:00:00"
    },
    nullStaffId: {
        appointmentId: 1,
        patientId: 1,
        staffId: null,
        method: "CASH",
        amount: 100000.0,
        paymentDate: "2025-08-04T08:00:00"
    }
};

async function testPaymentCreation(paymentData, testName) {
    console.log(`\n=== Testing ${testName} ===`);
    
    try {
        const response = await fetch(`${BASE_URL}/api/payments`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': 'Bearer eyJhbGciOiJIUzI1NiJ9.***********************************************************************************.YOUR_TOKEN_HERE'
            },
            body: JSON.stringify(paymentData)
        });

        const responseText = await response.text();
        console.log(`Status: ${response.status}`);
        console.log(`Response: ${responseText}`);

        if (response.ok) {
            console.log(`✅ ${testName} - SUCCESS`);
            return JSON.parse(responseText);
        } else {
            console.log(`❌ ${testName} - FAILED`);
            console.log(`Error: ${responseText}`);
            return null;
        }
    } catch (error) {
        console.log(`❌ ${testName} - ERROR: ${error.message}`);
        return null;
    }
}

async function runNullValidationTests() {
    console.log("🧪 Payment Null Validation Tests");
    console.log("==================================");
    
    // Test 1: Null Appointment ID
    await testPaymentCreation(testPaymentData.nullAppointmentId, "Null Appointment ID");
    
    // Test 2: Null Patient ID  
    await testPaymentCreation(testPaymentData.nullPatientId, "Null Patient ID");
    
    // Test 3: Null Staff ID
    await testPaymentCreation(testPaymentData.nullStaffId, "Null Staff ID");
    
    // Test 4: Valid data (if IDs exist)
    await testPaymentCreation(testPaymentData.valid, "Valid Payment Data");
    
    console.log("\n🏁 Tests completed!");
    console.log("Expected results:");
    console.log("- Null ID tests should return 400/500 with clear error messages");
    console.log("- Valid test may fail if appointment/patient/staff don't exist");
    console.log("- Key improvement: Clear error messages instead of generic JPA errors");
}

// Uncomment to run tests
// runNullValidationTests();

// Export for manual testing
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        testPaymentData,
        testPaymentCreation,
        runNullValidationTests
    };
}

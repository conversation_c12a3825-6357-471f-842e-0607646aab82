// Test Updated Active Schedule Filter for Doctor Leave Request (No Week Limitation)
// This test verifies that ALL ACTIVE schedules are shown (not limited to current week)

const testScheduleData = [
    { id: 1, date: '2025-08-05', startTime: '08:00:00', endTime: '09:00:00', status: 'ACTIVE' },
    { id: 2, date: '2025-08-05', startTime: '10:00:00', endTime: '11:00:00', status: 'CANCELLED' },
    { id: 3, date: '2025-08-06', startTime: '14:00:00', endTime: '15:00:00', status: 'ACTIVE' },
    { id: 4, date: '2025-08-03', startTime: '08:00:00', endTime: '09:00:00', status: 'ACTIVE' }, // Past date
    { id: 5, date: '2025-08-15', startTime: '16:00:00', endTime: '17:00:00', status: 'ACTIVE' }, // Next week
    { id: 6, date: '2025-08-20', startTime: '09:00:00', endTime: '10:00:00', status: 'ACTIVE' }, // Weeks later
    { id: 7, date: '2025-09-10', startTime: '14:00:00', endTime: '15:00:00', status: 'ACTIVE' }, // Next month
    { id: 8, date: '2025-08-07', startTime: '11:00:00', endTime: '12:00:00', status: 'INACTIVE' }
];

// Helper function to parse date like in the component
function parseLocalDate(dateStr) {
    const [year, month, day] = dateStr.split('-').map(Number);
    return new Date(year, month - 1, day);
}

function testUpdatedActiveScheduleFiltering() {
    console.log("🧪 Testing Updated Active Schedule Filtering (No Week Limitation)");
    console.log("================================================================");
    
    // Simulate current date (assuming today is 2025-08-04)
    const today = new Date(2025, 7, 4); // August 4, 2025
    const todayForComparison = new Date(today);
    todayForComparison.setHours(0, 0, 0, 0);
    
    console.log(`Today: ${today.toLocaleDateString()}`);
    
    // Updated filter (removed week limitation)
    const activeSlotsForLeaveRequest = testScheduleData.filter(s => {
        const d = parseLocalDate(s.date);
        return s.status === 'ACTIVE' && d >= todayForComparison;
    }).sort((a, b) => {
        // Sort by date first, then by start time
        const dateA = parseLocalDate(a.date);
        const dateB = parseLocalDate(b.date);
        if (dateA.getTime() !== dateB.getTime()) {
            return dateA.getTime() - dateB.getTime();
        }
        return a.startTime.localeCompare(b.startTime);
    });
    
    console.log("\n📋 All Schedules:");
    testScheduleData.forEach(s => {
        const date = parseLocalDate(s.date);
        const isPast = date < todayForComparison;
        const daysDiff = Math.floor((date - todayForComparison) / (1000 * 60 * 60 * 24));
        console.log(`  ID: ${s.id}, Date: ${s.date}, Status: ${s.status}, Past: ${isPast}, Days from today: ${daysDiff}`);
    });
    
    console.log("\n✅ Filtered ACTIVE Schedules for Leave Request (ALL future dates):");
    if (activeSlotsForLeaveRequest.length > 0) {
        activeSlotsForLeaveRequest.forEach(s => {
            const date = parseLocalDate(s.date);
            const daysDiff = Math.floor((date - todayForComparison) / (1000 * 60 * 60 * 24));
            console.log(`  ✓ ID: ${s.id}, Date: ${s.date}, Time: ${s.startTime.slice(0,5)} - ${s.endTime.slice(0,5)}, Status: ${s.status}, +${daysDiff} days`);
        });
    } else {
        console.log("  ⚠️ No ACTIVE schedules available for leave request");
    }
    
    console.log("\n❌ Excluded Schedules (should NOT appear in leave request form):");
    const excludedSchedules = testScheduleData.filter(s => !activeSlotsForLeaveRequest.includes(s));
    excludedSchedules.forEach(s => {
        const date = parseLocalDate(s.date);
        const isPast = date < todayForComparison;
        const reason = isPast ? 'Past date' : 
                      s.status !== 'ACTIVE' ? `Status: ${s.status}` : 'Unknown';
        console.log(`  ✗ ID: ${s.id}, Date: ${s.date}, Status: ${s.status}, Reason: ${reason}`);
    });
    
    console.log("\n📊 Summary:");
    console.log(`Total schedules: ${testScheduleData.length}`);
    console.log(`ACTIVE schedules available for leave request: ${activeSlotsForLeaveRequest.length}`);
    console.log(`Excluded schedules: ${excludedSchedules.length}`);
    
    // Validation for updated logic (should include all future ACTIVE schedules)
    const expectedActiveIds = [1, 3, 5, 6, 7]; // All future ACTIVE schedules (removed week limitation)
    const actualActiveIds = activeSlotsForLeaveRequest.map(s => s.id);
    
    console.log("\n🔍 Validation:");
    console.log(`Expected IDs (all future ACTIVE): [${expectedActiveIds.join(', ')}]`);
    console.log(`Actual IDs: [${actualActiveIds.join(', ')}]`);
    
    const isCorrect = expectedActiveIds.every(id => actualActiveIds.includes(id)) && 
                      actualActiveIds.every(id => expectedActiveIds.includes(id));
    
    if (isCorrect) {
        console.log("✅ Updated filter working correctly!");
    } else {
        console.log("❌ Updated filter has issues!");
    }
    
    // Test sorting
    console.log("\n📅 Sorting Verification:");
    let isSorted = true;
    for (let i = 1; i < activeSlotsForLeaveRequest.length; i++) {
        const prevDate = parseLocalDate(activeSlotsForLeaveRequest[i-1].date);
        const currDate = parseLocalDate(activeSlotsForLeaveRequest[i].date);
        if (currDate < prevDate) {
            isSorted = false;
            break;
        }
        if (currDate.getTime() === prevDate.getTime()) {
            // Same date, check time sorting
            if (activeSlotsForLeaveRequest[i].startTime < activeSlotsForLeaveRequest[i-1].startTime) {
                isSorted = false;
                break;
            }
        }
    }
    
    console.log(`Chronological sorting: ${isSorted ? '✅ Correct' : '❌ Incorrect'}`);
    
    return {
        totalSchedules: testScheduleData.length,
        filteredSchedules: activeSlotsForLeaveRequest.length,
        excludedSchedules: excludedSchedules.length,
        isFilterCorrect: isCorrect,
        isSorted: isSorted,
        activeSlots: activeSlotsForLeaveRequest
    };
}

// Test comparison between old and new logic
function testFilterComparison() {
    console.log("\n🧪 Comparing Old vs New Filter Logic");
    console.log("=====================================");
    
    const today = new Date(2025, 7, 4); // August 4, 2025
    const todayForComparison = new Date(today);
    todayForComparison.setHours(0, 0, 0, 0);
    
    // Simulate week calculation
    function getStartOfWeek(date) {
        const d = new Date(date);
        const day = d.getDay();
        const diff = d.getDate() - ((day === 0 ? 7 : day) - 1);
        d.setDate(diff);
        d.setHours(0, 0, 0, 0);
        return d;
    }
    
    const weekStart = getStartOfWeek(today);
    
    // Old logic (with week limitation)
    const oldFilter = testScheduleData.filter(s => {
        const d = parseLocalDate(s.date);
        return s.status === 'ACTIVE' && d >= todayForComparison && d >= weekStart && d < new Date(weekStart.getTime() + 7 * 86400000);
    });
    
    // New logic (no week limitation)
    const newFilter = testScheduleData.filter(s => {
        const d = parseLocalDate(s.date);
        return s.status === 'ACTIVE' && d >= todayForComparison;
    });
    
    console.log(`Week start: ${weekStart.toLocaleDateString()}`);
    console.log(`Week end: ${new Date(weekStart.getTime() + 6 * 86400000).toLocaleDateString()}`);
    
    console.log("\n📊 Filter Results:");
    console.log(`Old filter (week limited): ${oldFilter.length} schedules`);
    oldFilter.forEach(s => console.log(`  - ID: ${s.id}, Date: ${s.date}`));
    
    console.log(`\nNew filter (no week limit): ${newFilter.length} schedules`);
    newFilter.forEach(s => console.log(`  - ID: ${s.id}, Date: ${s.date}`));
    
    const additionalSchedules = newFilter.filter(s => !oldFilter.includes(s));
    console.log(`\n➕ Additional schedules now available: ${additionalSchedules.length}`);
    additionalSchedules.forEach(s => {
        const date = parseLocalDate(s.date);
        const daysDiff = Math.floor((date - todayForComparison) / (1000 * 60 * 60 * 24));
        console.log(`  + ID: ${s.id}, Date: ${s.date}, +${daysDiff} days from today`);
    });
    
    return {
        oldFilterCount: oldFilter.length,
        newFilterCount: newFilter.length,
        additionalSchedules: additionalSchedules.length
    };
}

// Run tests
console.log("Running Updated Active Schedule Filter Tests...\n");
const results = testUpdatedActiveScheduleFiltering();
const comparison = testFilterComparison();

console.log("\n🏁 Test Complete!");
console.log("==================");
console.log("The updated filter now ensures that:");
console.log("✅ Only ACTIVE status schedules are shown");
console.log("✅ Only current and future dates are shown");
console.log("❌ NO WEEK LIMITATION (doctors can select any future ACTIVE schedule)");
console.log("✅ Schedules are sorted chronologically");
console.log("✅ Clear message when no ACTIVE schedules available");

console.log("\n📈 Improvement Summary:");
console.log(`Before: ${comparison.oldFilterCount} schedules (week limited)`);
console.log(`After: ${comparison.newFilterCount} schedules (no week limit)`);
console.log(`Additional options: +${comparison.additionalSchedules} schedules`);

// Export for manual testing
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        testUpdatedActiveScheduleFiltering,
        testFilterComparison,
        testScheduleData
    };
}

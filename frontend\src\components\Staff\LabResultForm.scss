.form-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.6);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.form-container {
    background: #fff;
    padding: 2.5rem;
    border-radius: 8px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
    width: 90%;
    max-width: 900px;
    max-height: 90vh;
    overflow-y: auto;

    h2 {
        margin-top: 0;
        margin-bottom: 2rem;
        text-align: center;
    }
}

.form-group {
    margin-bottom: 1rem;

    label {
        display: block;
        margin-bottom: 0.5rem;
        font-weight: 500;
    }

    input[type="text"],
    input[type="date"],
    select,
    textarea {
        width: 100%;
        padding: 0.75rem;
        border: 1px solid #ccc;
        border-radius: 4px;
        font-size: 1rem;
        box-sizing: border-box;

        &:read-only {
            background-color: #f5f5f5;
            cursor: not-allowed;
        }
    }

    textarea {
        min-height: 80px;
        resize: vertical;
    }
}

.form-group-inline {
    display: flex;
    gap: 1rem;

    .form-group {
        flex: 1;
    }
}

.form-actions {
    margin-top: 1.5rem;
    display: flex;
    justify-content: flex-end;
    gap: 1rem;

    button {
        padding: 0.75rem 1.5rem;
        border: none;
        border-radius: 4px;
        font-size: 1rem;
        cursor: pointer;
        transition: background-color 0.3s ease;

        &[type="submit"] {
            background-color: #007bff;
            color: white;

            &:disabled {
                background-color: #a0cffa;
                cursor: not-allowed;
            }

            &:not(:disabled):hover {
                background-color: #0056b3;
            }
        }
    }

    .btn-cancel {
        background-color: #6c757d;
        color: white;

        &:hover {
            background-color: #5a6268;
        }
    }
}

.error-message {
    color: #f44336;
    margin-bottom: 1rem;
    text-align: center;
}

.result-row {
    border: 1px solid #e0e0e0;
    padding: 1rem;
    margin-bottom: 1.5rem;
    border-radius: 6px;
    position: relative;
    background-color: #f9f9f9;

    h4 {
        margin-top: 0;
        margin-bottom: 1rem;
        border-bottom: 1px solid #ddd;
        padding-bottom: 0.5rem;
    }
}

.btn-remove-row {
    position: absolute;
    top: 10px;
    right: 10px;
    background: #f44336;
    color: white;
    border: none;
    border-radius: 50%;
    width: 24px;
    height: 24px;
    font-size: 16px;
    line-height: 24px;
    text-align: center;
    cursor: pointer;
    padding: 0;

    &:hover {
        background: #d32f2f;
    }
}

.btn-add-row {
    display: block;
    width: 100%;
    padding: 0.75rem;
    margin-top: -0.5rem;
    margin-bottom: 1.5rem;
    background-color: #e8f5e9;
    color: #388e3c;
    border: 1px dashed #c8e6c9;
    border-radius: 4px;
    text-align: center;
    cursor: pointer;
    transition: background-color 0.3s ease;

    &:hover {
        background-color: #c8e6c9;
    }
}
package com.group7.hivcare.hivtreatmentmedicalservicesystem.user.controller;

import com.group7.hivcare.hivtreatmentmedicalservicesystem.common.entity.User;
import com.group7.hivcare.hivtreatmentmedicalservicesystem.user.dto.UserDTO;
import com.group7.hivcare.hivtreatmentmedicalservicesystem.user.dto.UserDeleteDTO;
import com.group7.hivcare.hivtreatmentmedicalservicesystem.user.dto.UserUpdateDTO;
import com.group7.hivcare.hivtreatmentmedicalservicesystem.user.dto.PasswordUpdateDTO;
import com.group7.hivcare.hivtreatmentmedicalservicesystem.user.service.UserService;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.web.bind.annotation.*;

import java.util.Optional;

@RestController
@RequestMapping("/api/users")
public class UserController {
    @Autowired
    private UserService userService;

    // Cập nhật thông tin người dùng hiện tại
    @PutMapping("/me")
    @PreAuthorize("isAuthenticated()")
    public ResponseEntity<UserDTO> updateCurrentUser(
            @AuthenticationPrincipal UserDetails userDetails,
            @Valid @RequestBody UserUpdateDTO userUpdateDTO) {
        UserDTO updatedUser = userService.updateCurrentUserProfile(userUpdateDTO);
        return ResponseEntity.ok(updatedUser);
    }

    // Lấy thông tin người dùng hiện tại
    @GetMapping("/me")
    @PreAuthorize("isAuthenticated()")
    public ResponseEntity<UserDTO> getCurrentUser(@AuthenticationPrincipal UserDetails userDetails) {
        return userService.findByEmail(userDetails.getUsername())
                .map(ResponseEntity::ok)
                .orElseGet(() -> ResponseEntity.notFound().build());
    }

    // Deactivate tài khoản của chính mình
    @DeleteMapping("/me")
    @PreAuthorize("isAuthenticated()")
    public ResponseEntity<String> deactivateMyAccount(@AuthenticationPrincipal UserDetails userDetails) {
        userService.findByEmail(userDetails.getUsername())
                .ifPresent(user -> userService.deactivateUser(user.getId()));
        return ResponseEntity.ok("Tài khoản đã được vô hiệu hóa thành công");
    }

    // Cập nhật mật khẩu
    @PutMapping("/me/password")
    @PreAuthorize("isAuthenticated()")
    public ResponseEntity<String> updatePassword(
            @AuthenticationPrincipal UserDetails userDetails,
            @Valid @RequestBody PasswordUpdateDTO passwordUpdateDTO) {
        userService.updatePassword(passwordUpdateDTO);
        return ResponseEntity.ok("Mật khẩu đã được cập nhật thành công");
    }
}

.doctor-blog-page {
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
    padding: 32px;
    margin-bottom: 32px;
    max-width: 900px;
    margin: 0 auto;

    h2 {
        font-size: 1.5rem;
        font-weight: 600;
        margin-bottom: 24px;
        color: #1a237e;
    }

    .blog-form {
        margin-bottom: 32px;

        .form-group {
            margin-bottom: 18px;

            label {
                display: block;
                font-weight: 500;
                margin-bottom: 8px;
                color: #333;
            }

            input[type="text"],
            textarea {
                width: 100%;
                padding: 10px 12px;
                border: 1px solid #ddd;
                border-radius: 6px;
                font-size: 15px;
                transition: border-color 0.2s;

                &:focus {
                    border-color: #1a73e8;
                    outline: none;
                }
            }

            input[type="file"] {
                margin-top: 6px;
            }

            .blog-preview-img {
                margin-top: 10px;
                max-width: 180px;
                max-height: 120px;
                border-radius: 8px;
                box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08);
                display: block;
            }
        }

        .form-actions {
            display: flex;
            gap: 12px;
            margin-top: 10px;

            .save-button {
                background: #1a73e8;
                color: #fff;
                border: none;
                padding: 10px 24px;
                border-radius: 6px;
                font-weight: 500;
                font-size: 15px;
                cursor: pointer;
                transition: background 0.2s;

                &:hover {
                    background: #1557b0;
                }

                &:disabled {
                    background: #ccc;
                    cursor: not-allowed;
                }
            }

            .cancel-button {
                background: #fff;
                color: #666;
                border: 1px solid #ddd;
                padding: 10px 24px;
                border-radius: 6px;
                font-weight: 500;
                font-size: 15px;
                cursor: pointer;

                &:hover {
                    background: #f8f9fa;
                }
            }
        }
    }

    .blog-filter {
        margin-bottom: 18px;

        input {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 6px;
            font-size: 15px;
        }
    }

    h3 {
        font-size: 1.2rem;
        font-weight: 600;
        margin: 18px 0 12px 0;
        color: #222;
    }

    .blog-list {
        display: flex;
        flex-direction: column;
        gap: 18px;
    }

    .blog-item {
        background: #fafbfc;
        border-radius: 10px;
        box-shadow: 0 1px 4px rgba(0, 0, 0, 0.04);
        padding: 18px 22px;
        position: relative;

        .blog-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 8px;

            h4 {
                margin: 0;
                font-size: 1.1rem;
                font-weight: 600;
                color: #1a237e;
            }

            .blog-status {
                font-size: 0.95rem;
                font-weight: 500;
                padding: 4px 12px;
                border-radius: 12px;

                &.published {
                    background: #e3fcec;
                    color: #137333;
                }

                &.draft {
                    background: #fff3cd;
                    color: #b8860b;
                }
            }
        }

        .blog-preview-img {
            max-width: 160px;
            max-height: 100px;
            border-radius: 8px;
            margin-bottom: 8px;
            box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08);
            display: block;
        }

        .blog-content {
            color: #222;
            font-size: 1rem;
            margin-bottom: 10px;
            white-space: pre-line;
        }

        .blog-actions {
            display: flex;
            gap: 10px;

            button {
                background: #fff;
                color: #1a73e8;
                border: 1px solid #1a73e8;
                border-radius: 6px;
                padding: 6px 16px;
                font-size: 14px;
                font-weight: 500;
                cursor: pointer;
                transition: background 0.2s, color 0.2s;

                &:hover {
                    background: #1a73e8;
                    color: #fff;
                }
            }
        }
    }

    @media (max-width: 768px) {
        padding: 12px;

        .blog-form,
        .blog-item {
            padding: 10px;
        }

        .blog-header h4 {
            font-size: 1rem;
        }

        .blog-content {
            font-size: 0.98rem;
        }

        .blog-preview-img {
            max-width: 100px;
            max-height: 60px;
        }
    }
}

.blog-modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.18);
    z-index: 1000;
    display: flex;
    align-items: center;
    justify-content: center;
}

.blog-modal {
    background: #fff;
    border-radius: 16px;
    box-shadow: 0 4px 32px rgba(60, 64, 67, 0.15);
    padding: 48px 64px 40px 64px;
    min-width: 900px;
    max-width: 1200px;
    position: relative;
    font-family: 'Google Sans', Arial, sans-serif;

    h3 {
        margin-top: 0;
        margin-bottom: 2rem;
        font-size: 1.7rem;
        font-weight: 600;
        color: #222;
        text-align: center;
    }

    .blog-form {
        .form-group {

            input[type="text"],
            textarea {
                font-size: 1.25rem;
                padding: 18px 22px;
            }
        }
    }
}

@media (max-width: 1300px) {
    .blog-modal {
        min-width: 98vw;
        max-width: 100vw;
        padding: 12px 2vw;
    }
}

// Comment styles
.comment-container {
    word-wrap: break-word;
    overflow-wrap: break-word;
    word-break: break-word;

    .comment-content {
        white-space: pre-wrap;
        line-height: 1.5;
        max-width: 100%;
    }

    .comment-expand-btn {
        background: none;
        border: none;
        color: #1976d2;
        cursor: pointer;
        font-size: 12px;
        margin-top: 4px;
        text-decoration: underline;

        &:hover {
            color: #1557b0;
        }
    }
}

// Like button animation
@keyframes pulse {
    0% {
        transform: scale(1);
    }

    50% {
        transform: scale(1.1);
    }

    100% {
        transform: scale(1);
    }
}

@keyframes heartBeat {
    0% {
        transform: scale(1);
    }

    14% {
        transform: scale(1.3);
    }

    28% {
        transform: scale(1);
    }

    42% {
        transform: scale(1.3);
    }

    70% {
        transform: scale(1);
    }
}

.like-button {
    transition: all 0.3s ease;

    &.liked {
        animation: heartBeat 1.5s ease-in-out;
    }

    &:hover {
        transform: scale(1.05);
    }
}
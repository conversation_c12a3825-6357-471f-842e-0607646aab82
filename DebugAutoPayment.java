import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.Statement;
import java.time.LocalDate;
import java.time.LocalTime;

public class DebugAutoPayment {
    
    private static final String JDBC_URL = "***************************************************************************************";
    private static final String USERNAME = "sa";
    private static final String PASSWORD = "123456";
    
    public static void main(String[] args) {
        System.out.println("=== Debug Auto Payment Creation ===");
        
        try (Connection conn = DriverManager.getConnection(JDBC_URL, USERNAME, PASSWORD)) {
            
            // 1. <PERSON><PERSON>m tra các appointment có status CONFIRMED và có thể test check-in
            System.out.println("\n1. Checking CONFIRMED appointments for today:");
            checkConfirmedAppointments(conn);
            
            // 2. <PERSON><PERSON><PERSON> tra các appointment đã CHECKED_IN nhưng chưa có payment
            System.out.println("\n2. Checking CHECKED_IN appointments without payments:");
            checkCheckedInWithoutPayments(conn);
            
            // 3. Kiểm tra các payment đã được tạo
            System.out.println("\n3. Checking existing payments:");
            checkExistingPayments(conn);
            
            // 4. Thử tạo một appointment test để check-in
            System.out.println("\n4. Creating test appointment for check-in:");
            createTestAppointment(conn);
            
        } catch (Exception e) {
            System.err.println("Error: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    private static void checkConfirmedAppointments(Connection conn) throws Exception {
        String sql = """
            SELECT TOP 5 
                a.id, 
                a.appointment_date, 
                a.appointment_time, 
                a.status,
                p.id as patient_id,
                u.full_name as patient_name,
                ms.name as service_name,
                ms.price
            FROM appointments a
            JOIN patients p ON a.patient_id = p.id
            JOIN users u ON p.user_id = u.id
            JOIN medical_services ms ON a.medical_service_id = ms.id
            WHERE a.status = 'CONFIRMED'
            ORDER BY a.appointment_date DESC, a.appointment_time DESC
            """;
            
        try (PreparedStatement stmt = conn.prepareStatement(sql);
             ResultSet rs = stmt.executeQuery()) {
            
            boolean found = false;
            while (rs.next()) {
                found = true;
                System.out.printf("  Appointment ID: %d | Date: %s | Time: %s | Patient: %s (ID: %d) | Service: %s | Price: %.2f%n",
                    rs.getInt("id"),
                    rs.getDate("appointment_date"),
                    rs.getTime("appointment_time"),
                    rs.getString("patient_name"),
                    rs.getInt("patient_id"),
                    rs.getString("service_name"),
                    rs.getDouble("price")
                );
            }
            
            if (!found) {
                System.out.println("  No CONFIRMED appointments found");
            }
        }
    }
    
    private static void checkCheckedInWithoutPayments(Connection conn) throws Exception {
        String sql = """
            SELECT 
                a.id as appointment_id, 
                a.appointment_date, 
                a.appointment_time, 
                a.status,
                p.id as patient_id,
                u.full_name as patient_name,
                ms.name as service_name,
                ms.price,
                pay.id as payment_id
            FROM appointments a
            JOIN patients p ON a.patient_id = p.id
            JOIN users u ON p.user_id = u.id
            JOIN medical_services ms ON a.medical_service_id = ms.id
            LEFT JOIN payments pay ON a.id = pay.appointment_id
            WHERE a.status = 'CHECKED_IN' AND pay.id IS NULL
            ORDER BY a.appointment_date DESC, a.appointment_time DESC
            """;
            
        try (PreparedStatement stmt = conn.prepareStatement(sql);
             ResultSet rs = stmt.executeQuery()) {
            
            boolean found = false;
            while (rs.next()) {
                found = true;
                System.out.printf("  ❌ Appointment ID: %d | Date: %s | Time: %s | Patient: %s | Service: %s | Price: %.2f - NO PAYMENT!%n",
                    rs.getInt("appointment_id"),
                    rs.getDate("appointment_date"),
                    rs.getTime("appointment_time"),
                    rs.getString("patient_name"),
                    rs.getString("service_name"),
                    rs.getDouble("price")
                );
            }
            
            if (!found) {
                System.out.println("  ✅ All CHECKED_IN appointments have payments");
            }
        }
    }
    
    private static void checkExistingPayments(Connection conn) throws Exception {
        String sql = """
            SELECT TOP 5
                p.id as payment_id,
                p.amount,
                p.status,
                p.method,
                p.notes,
                p.payment_date,
                a.id as appointment_id,
                a.status as appointment_status
            FROM payments p
            JOIN appointments a ON p.appointment_id = a.id
            ORDER BY p.payment_date DESC
            """;
            
        try (PreparedStatement stmt = conn.prepareStatement(sql);
             ResultSet rs = stmt.executeQuery()) {
            
            boolean found = false;
            while (rs.next()) {
                found = true;
                System.out.printf("  Payment ID: %d | Amount: %.2f | Status: %s | Method: %s | Appointment ID: %d | Appointment Status: %s%n",
                    rs.getInt("payment_id"),
                    rs.getDouble("amount"),
                    rs.getString("status"),
                    rs.getString("method"),
                    rs.getInt("appointment_id"),
                    rs.getString("appointment_status")
                );
                
                if (rs.getString("notes") != null && rs.getString("notes").contains("tự động")) {
                    System.out.println("    📝 " + rs.getString("notes"));
                }
            }
            
            if (!found) {
                System.out.println("  No payments found in database");
            }
        }
    }
    
    private static void createTestAppointment(Connection conn) throws Exception {
        // Tìm patient và doctor ID đầu tiên
        Integer patientId = null;
        Integer doctorId = null;
        Integer serviceId = null;
        
        // Get patient ID
        String getPatientSql = "SELECT TOP 1 id FROM patients";
        try (Statement stmt = conn.createStatement();
             ResultSet rs = stmt.executeQuery(getPatientSql)) {
            if (rs.next()) {
                patientId = rs.getInt("id");
            }
        }
        
        // Get doctor ID
        String getDoctorSql = "SELECT TOP 1 id FROM doctors";
        try (Statement stmt = conn.createStatement();
             ResultSet rs = stmt.executeQuery(getDoctorSql)) {
            if (rs.next()) {
                doctorId = rs.getInt("id");
            }
        }
        
        // Get service ID
        String getServiceSql = "SELECT TOP 1 id FROM medical_services";
        try (Statement stmt = conn.createStatement();
             ResultSet rs = stmt.executeQuery(getServiceSql)) {
            if (rs.next()) {
                serviceId = rs.getInt("id");
            }
        }
        
        if (patientId != null && doctorId != null && serviceId != null) {
            
            // Tạo appointment mới với status CONFIRMED
            String insertSql = """
                INSERT INTO appointments (
                    patient_id, doctor_id, medical_service_id, 
                    appointment_date, appointment_time, status,
                    created_at, updated_at
                ) VALUES (?, ?, ?, ?, ?, 'CONFIRMED', GETDATE(), GETDATE())
                """;
                
            try (PreparedStatement stmt = conn.prepareStatement(insertSql, Statement.RETURN_GENERATED_KEYS)) {
                stmt.setInt(1, patientId);
                stmt.setInt(2, doctorId);
                stmt.setInt(3, serviceId);
                stmt.setDate(4, java.sql.Date.valueOf(LocalDate.now()));
                stmt.setTime(5, java.sql.Time.valueOf(LocalTime.now().withMinute(0).withSecond(0)));
                
                int result = stmt.executeUpdate();
                if (result > 0) {
                    try (ResultSet keys = stmt.getGeneratedKeys()) {
                        if (keys.next()) {
                            int appointmentId = keys.getInt(1);
                            System.out.printf("  ✅ Created test appointment ID: %d (Patient: %d, Doctor: %d, Service: %d)%n", 
                                appointmentId, patientId, doctorId, serviceId);
                            System.out.println("  📝 You can now test check-in with this appointment");
                            
                            // Hiển thị curl command để test
                            System.out.println("\n  🧪 Test check-in with curl command:");
                            System.out.printf("  curl -X POST http://localhost:8080/api/appointments/%d/checkin \\%n", appointmentId);
                            System.out.println("       -H \"Content-Type: application/json\" \\");
                            System.out.println("       -H \"Authorization: Bearer YOUR_JWT_TOKEN\"");
                        }
                    }
                }
            }
        } else {
            System.out.println("  ❌ Cannot create test appointment - missing patient/doctor/service data");
        }
    }
}

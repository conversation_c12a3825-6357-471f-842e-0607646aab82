// Test script to verify automatic payment creation after patient check-in
// Run this script after backend is running

const BASE_URL = 'http://localhost:8080/api';

// Test function to simulate patient check-in and verify auto payment creation
async function testAutoPaymentCreation() {
    console.log('=== Testing Auto Payment Creation ===');
    
    try {
        // 1. First, let's get a list of appointments that can be checked in
        console.log('1. Fetching appointments...');
        const appointmentsResponse = await fetch(`${BASE_URL}/appointments`, {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json'
            }
        });
        
        if (!appointmentsResponse.ok) {
            console.log('Appointments endpoint might need authentication');
            console.log('Response status:', appointmentsResponse.status);
            return;
        }
        
        const appointments = await appointmentsResponse.json();
        console.log('Found appointments:', appointments.length);
        
        // Find a CONFIRMED appointment that can be checked in
        const confirmedAppointment = appointments.find(apt => 
            apt.status === 'CONFIRMED' && 
            apt.appointmentDate === new Date().toISOString().split('T')[0]
        );
        
        if (!confirmedAppointment) {
            console.log('No CONFIRMED appointments found for today to test check-in');
            
            // Let's check if there are any appointments at all
            if (appointments.length > 0) {
                console.log('Available appointments:');
                appointments.slice(0, 3).forEach(apt => {
                    console.log(`- ID: ${apt.id}, Status: ${apt.status}, Date: ${apt.appointmentDate}, Patient: ${apt.patientId}`);
                });
            }
            
            // For testing, we'll try with the first appointment if exists
            if (appointments.length > 0) {
                const testAppointment = appointments[0];
                console.log(`\n2. Testing with appointment ID: ${testAppointment.id}`);
                console.log(`   Current status: ${testAppointment.status}`);
                console.log(`   Patient ID: ${testAppointment.patientId}`);
                
                // Try to check-in this appointment
                await attemptCheckIn(testAppointment.id, testAppointment.patientId);
            }
            return;
        }
        
        console.log(`\n2. Found CONFIRMED appointment to test:`);
        console.log(`   ID: ${confirmedAppointment.id}`);
        console.log(`   Patient ID: ${confirmedAppointment.patientId}`);
        console.log(`   Date: ${confirmedAppointment.appointmentDate}`);
        console.log(`   Time: ${confirmedAppointment.appointmentTime}`);
        
        // 3. Attempt patient check-in
        await attemptCheckIn(confirmedAppointment.id, confirmedAppointment.patientId);
        
    } catch (error) {
        console.error('Error during test:', error.message);
    }
}

async function attemptCheckIn(appointmentId, patientId) {
    console.log(`\n3. Attempting check-in for appointment ${appointmentId}...`);
    
    try {
        const checkInResponse = await fetch(`${BASE_URL}/appointments/${appointmentId}/check-in`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                patientId: patientId
            })
        });
        
        if (checkInResponse.ok) {
            const result = await checkInResponse.json();
            console.log('✅ Check-in successful!');
            console.log('Updated appointment status:', result.status);
            
            // 4. Now check if payment was automatically created
            console.log('\n4. Checking for auto-created payment...');
            await checkPaymentCreation(appointmentId);
            
        } else {
            const errorText = await checkInResponse.text();
            console.log('❌ Check-in failed:');
            console.log('Status:', checkInResponse.status);
            console.log('Error:', errorText);
        }
        
    } catch (error) {
        console.error('Error during check-in:', error.message);
    }
}

async function checkPaymentCreation(appointmentId) {
    try {
        // Check if payment was created for this appointment
        const paymentsResponse = await fetch(`${BASE_URL}/payments?appointmentId=${appointmentId}`, {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json'
            }
        });
        
        if (paymentsResponse.ok) {
            const payments = await paymentsResponse.json();
            
            if (payments && payments.length > 0) {
                console.log('✅ Auto payment creation SUCCESS!');
                payments.forEach(payment => {
                    console.log(`   Payment ID: ${payment.id}`);
                    console.log(`   Amount: ${payment.amount}`);
                    console.log(`   Status: ${payment.status}`);
                    console.log(`   Method: ${payment.method}`);
                    console.log(`   Notes: ${payment.notes}`);
                });
            } else {
                console.log('❌ No payments found for this appointment');
                console.log('Auto payment creation might have failed');
            }
        } else {
            console.log('Could not fetch payments - endpoint might need different URL or auth');
            console.log('Response status:', paymentsResponse.status);
        }
        
    } catch (error) {
        console.error('Error checking payments:', error.message);
    }
}

// Alternative test using direct API endpoints
async function testPaymentEndpoints() {
    console.log('\n=== Testing Payment Endpoints ===');
    
    try {
        // Test payments endpoint
        const response = await fetch(`${BASE_URL}/payments`, {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json'
            }
        });
        
        console.log('Payments endpoint status:', response.status);
        
        if (response.ok) {
            const payments = await response.json();
            console.log('Total payments found:', payments.length);
            
            if (payments.length > 0) {
                console.log('Recent payments:');
                payments.slice(0, 3).forEach(payment => {
                    console.log(`- ID: ${payment.id}, Amount: ${payment.amount}, Status: ${payment.status}, Appointment: ${payment.appointmentId}`);
                });
            }
        }
        
    } catch (error) {
        console.error('Error testing payment endpoints:', error.message);
    }
}

// Run the tests
console.log('Starting auto payment creation test...');
console.log('Make sure the backend is running on localhost:8080');
console.log('');

// Wait for backend to be ready
setTimeout(async () => {
    await testAutoPaymentCreation();
    await testPaymentEndpoints();
}, 2000);

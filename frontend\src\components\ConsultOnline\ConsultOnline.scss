.consult-online-page {
    display: flex;
    gap: 48px;
    background: #fafbfc;
    border-radius: 18px;
    padding: 48px 32px;
    align-items: center;
    justify-content: center;
    min-height: 60vh;

    .consult-content {
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: flex-start;
        max-width: 520px;

        .consult-label {
            background: #e3e7fa;
            color: #3a3a3a;
            font-size: 1rem;
            font-weight: 600;
            padding: 6px 18px;
            border-radius: 8px;
            margin-bottom: 18px;
            display: inline-block;
        }

        .consult-title {
            font-size: 2.3rem;
            font-weight: 700;
            color: #181818;
            margin-bottom: 18px;
            line-height: 1.3;
        }

        .consult-desc {
            font-size: 1.15rem;
            color: #555;
            margin-bottom: 24px;
        }

        .consult-features {
            list-style: none;
            padding: 0;
            margin-bottom: 28px;
            width: 100%;

            li {
                display: flex;
                align-items: flex-start;
                gap: 14px;
                margin-bottom: 18px;

                .icon {
                    color: #007bff;
                    font-size: 1.5rem;
                    flex-shrink: 0;
                    margin-top: 2px;
                }

                div {
                    strong {
                        font-weight: 600;
                        color: #181818;
                        display: block;
                        margin-bottom: 4px;
                    }

                    span {
                        font-size: 1rem;
                        color: #666;
                    }
                }
            }
        }

        .consult-btn {
            background: #181818;
            color: #fff;
            border: none;
            border-radius: 8px;
            padding: 14px 32px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: background 0.2s, color 0.2s;
            margin-top: 8px;

            &:hover {
                background: #007bff;
                color: #fff;
            }
        }
    }

    .consult-image {
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: center;
        background: #f0f1f5;
        border-radius: 16px;
        min-height: 320px;
        min-width: 320px;

        .main-icon {
            font-size: 7rem;
            color: #007bff;
        }
    }
}

@media (max-width: 900px) {
    .consult-online-page {
        flex-direction: column;
        padding: 32px 10px;
        gap: 32px;

        .consult-content,
        .consult-image {
            max-width: 100%;
            min-width: 0;
        }

        .consult-image {
            min-height: 200px;
        }
    }
}
.homepage-services-section {
    background: #f9fbfc;
    padding: 60px 20px;

    .homepage-container {
        max-width: 1200px;
        margin: auto;
    }

    .homepage-section-header {
        text-align: center;
        margin-bottom: 40px;

        h2 {
            font-size: 32px;
            color: #2c3e50;
            margin-bottom: 10px;
        }

        p {
            font-size: 16px;
            color: #666;
        }
    }

    .homepage-services-grid {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: 24px;
    }

    @media (max-width: 900px) {
        .homepage-services-grid {
            grid-template-columns: repeat(2, 1fr);
        }
    }

    @media (max-width: 600px) {
        .homepage-services-grid {
            grid-template-columns: 1fr;
        }
    }

    .homepage-service-card {
        background: #fff;
        border-radius: 12px;
        padding: 24px;
        text-align: center;
        box-shadow: 0 8px 16px rgba(0, 0, 0, 0.05);
        transition: transform 0.25s ease, box-shadow 0.25s ease;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;

        &:hover {
            transform: translateY(-5px);
            box-shadow: 0 12px 24px rgba(0, 0, 0, 0.08);
        }

        .homepage-service-icon {
            font-size: 32px;
            color: #0288d1;
            margin-bottom: 16px;
        }

        h3 {
            font-size: 18px;
            color: #2c3e50;
            margin-bottom: 12px;
            font-weight: bold;
        }

        p {
            font-size: 14px;
            color: #555;
            margin-bottom: 20px;
        }

        .homepage-service-btn {
            background-color: #0288d1;
            color: #fff;
            padding: 10px 16px;
            border: none;
            border-radius: 6px;
            font-size: 14px;
            cursor: pointer;
            transition: background-color 0.3s;
            margin-top: 8px;

            &:hover {
                background-color: #0277bd;
            }
        }
    }
}
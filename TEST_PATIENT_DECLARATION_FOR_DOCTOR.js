// Test Medical Declaration API cho bác sĩ xem thông tin khai báo y tế
const API_BASE = 'http://localhost:8080/api';

async function testPatientDeclarationForDoctor() {
    try {
        console.log('🩺 Testing Patient Medical Declaration for Doctor...');

        // Test 1: <PERSON><PERSON><PERSON> khai báo theo appointment ID
        console.log('\n=== Test 1: <PERSON><PERSON>y khai báo theo appointment ID ===');
        
        const appointmentId = 2;
        
        const declarationResponse = await fetch(`${API_BASE}/appointment-declarations/appointment/${appointmentId}`, {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json'
            }
        });

        console.log('📡 Response status:', declarationResponse.status);
        
        if (declarationResponse.ok) {
            const declaration = await declarationResponse.json();
            console.log('✅ Medical declaration for appointment', appointmentId, ':', declaration);
            
            console.log('\n📋 Thông tin khai báo:');
            console.log('   - Mang thai:', declaration.isPregnant ? 'Có' : 'Không');
            console.log('   - <PERSON><PERSON><PERSON> chứng:', declaration.symptoms || 'Không có');
            console.log('   - <PERSON><PERSON> chú sức khỏe:', declaration.healthNotes || 'Không có');
            console.log('   - Thuốc đang dùng:', declaration.currentMedications || 'Không có');
            console.log('   - Dị ứng:', declaration.allergies || 'Không có');
            console.log('   - Liên hệ khẩn cấp:', declaration.emergencyContact || 'Không có');
            
        } else if (declarationResponse.status === 404) {
            console.log('ℹ️ Không có khai báo y tế cho appointment này');
        } else if (declarationResponse.status === 403) {
            console.log('🔒 Cần authentication để test API này');
            console.log('📝 Endpoints đã thêm authorization:');
            console.log('   - @PreAuthorize("hasAnyRole(\'PATIENT\', \'DOCTOR\', \'STAFF\', \'ADMIN\')")');
        } else {
            const error = await declarationResponse.text();
            console.log('❌ Lỗi:', error);
        }

        // Test 2: Lấy tất cả khai báo của bệnh nhân
        console.log('\n=== Test 2: Lấy tất cả khai báo của bệnh nhân ===');
        
        const patientId = 1;
        
        const patientDeclarationResponse = await fetch(`${API_BASE}/appointment-declarations/patient/${patientId}`, {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json'
            }
        });

        console.log('📡 Response status:', patientDeclarationResponse.status);
        
        if (patientDeclarationResponse.ok) {
            const declarations = await patientDeclarationResponse.json();
            console.log('✅ Tất cả khai báo của patient', patientId, ':', declarations);
            console.log('📊 Số lượng khai báo:', declarations?.length || 0);
            
            if (declarations && declarations.length > 0) {
                console.log('\n📋 Khai báo gần nhất:');
                const latest = declarations[0];
                console.log('   - ID:', latest.id);
                console.log('   - Appointment ID:', latest.appointmentId);
                console.log('   - Ngày tạo:', latest.createdAt);
                console.log('   - Mang thai:', latest.isPregnant ? 'Có' : 'Không');
            }
        } else if (patientDeclarationResponse.status === 403) {
            console.log('🔒 Cần authentication để test API này');
        } else {
            const error = await patientDeclarationResponse.text();
            console.log('❌ Lỗi:', error);
        }

        console.log('\n=== Frontend Integration ===');
        console.log('🎨 PatientMedicalDeclaration component đã được tạo:');
        console.log('   - Hiển thị đầy đủ thông tin khai báo y tế');
        console.log('   - Tích hợp vào PatientDetailModal');
        console.log('   - Có styling riêng với PatientMedicalDeclaration.scss');
        console.log('   - Sử dụng AppointmentDeclarationService');
        
        console.log('\n📱 UI Features:');
        console.log('   ✅ Icons cho từng loại thông tin');
        console.log('   ✅ Color coding (mang thai = đỏ, không mang thai = xanh)');
        console.log('   ✅ Special styling cho dị ứng (nền vàng)');
        console.log('   ✅ Loading và error states');
        console.log('   ✅ Empty state khi không có khai báo');
        
        console.log('\n🔐 Backend Authorization:');
        console.log('   ✅ Bác sĩ có thể xem khai báo của bệnh nhân');
        console.log('   ✅ Staff và Admin cũng có quyền truy cập');
        console.log('   ✅ Bệnh nhân có thể xem khai báo của mình');

    } catch (error) {
        console.log('❌ Network error:', error.message);
    }
}

// Chạy test
testPatientDeclarationForDoctor();

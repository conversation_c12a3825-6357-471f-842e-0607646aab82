.form-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background: rgba(0,0,0,0.3);
    z-index: 2000;
    display: flex;
    align-items: center;
    justify-content: center;
}

.form-container {
    background: #fff;
    border-radius: 8px;
    padding: 32px 32px 24px 32px;
    max-width: 480px;
    width: 100%;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 4px 32px rgba(0,0,0,0.15);
    position: relative;
    z-index: 2001;

    h2 {
        margin-bottom: 0.25rem;
        font-size: 2rem;
        color: #222;
        text-align: left;
        font-weight: bold;
    }
    .form-desc {
        font-size: 1rem;
        color: #888;
        margin-bottom: 2rem;
        text-align: left;
    }

    form {
        display: flex;
        flex-direction: column;
        gap: 1.2rem;

        label {
            display: flex;
            align-items: center;
            font-size: 1.08rem;
            color: #222;
            font-weight: bold;
            margin-bottom: 0;
            gap: 16px;

            span.colon {
                color: #e74c3c;
                font-weight: bold;
                margin-right: 4px;
            }

            input,
            select {
                flex: 1;
                margin-top: 0;
                padding: 0.6rem 0.9rem;
                font-size: 1rem;
                background-color: #fff;
                color: #222;
                border: 1.5px solid #bbb;
                border-radius: 6px;
                width: 100%;
                box-sizing: border-box;
                font-weight: normal;
                transition: border-color 0.2s;
            }
            input:focus,
            select:focus {
                outline: none;
                border-color: #4a90e2;
                box-shadow: 0 0 3px rgba(74, 144, 226, 0.2);
            }
        }
        .checkbox-label {
            display: flex;
            align-items: center;
            font-size: 1.05rem;
            font-weight: normal;
            color: #222;
            margin-top: 0.2rem;
            gap: 8px;
            input[type="checkbox"] {
                margin-right: 8px;
                width: 18px;
                height: 18px;
            }
            .colon {
                color: #e74c3c;
                font-weight: bold;
                margin-right: 2px;
            }
        }
        .form-buttons {
            display: flex;
            justify-content: flex-end;
            gap: 1rem;
            margin-top: 1.5rem;
        }
    }
}

@keyframes slideIn {
    from {
        transform: translateY(-20px);
        opacity: 0;
    }

    to {
        transform: translateY(0);
        opacity: 1;
    }
}
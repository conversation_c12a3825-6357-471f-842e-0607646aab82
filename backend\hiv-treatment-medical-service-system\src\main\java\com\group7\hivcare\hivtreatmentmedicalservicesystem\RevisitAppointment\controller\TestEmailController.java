package com.group7.hivcare.hivtreatmentmedicalservicesystem.RevisitAppointment.controller;

import com.group7.hivcare.hivtreatmentmedicalservicesystem.RevisitAppointment.dto.CreateRevisitAppointmentDTO;
import com.group7.hivcare.hivtreatmentmedicalservicesystem.RevisitAppointment.dto.RevisitAppointmentDTO;
import com.group7.hivcare.hivtreatmentmedicalservicesystem.RevisitAppointment.service.RevisitAppointmentService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api/test")
@RequiredArgsConstructor
@Slf4j
public class TestEmailController {
    private final RevisitAppointmentService revisitAppointmentService;

    @PostMapping("/revisit-appointment")
    public ResponseEntity<RevisitAppointmentDTO> testCreateRevisitAppointment(
            @RequestBody CreateRevisitAppointmentDTO dto) {
        
        log.info("🔥 TEST: Tạo revisit appointment với appointmentId: {}", dto.getAppointmentId());
        log.info("🔥 TEST: Ngày tái khám: {}", dto.getRevisitDate());
        log.info("🔥 TEST: Ghi chú: {}", dto.getRevisitNotes());
        
        try {
            RevisitAppointmentDTO result = revisitAppointmentService.createRevisitAppointment(dto);
            log.info("✅ TEST: Tạo thành công revisit appointment ID: {}", result.getId());
            log.info("📧 TEST: Kiểm tra email có được gửi trong log");
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("❌ TEST: Lỗi tạo revisit appointment: ", e);
            throw e;
        }
    }
}

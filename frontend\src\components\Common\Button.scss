.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    border: none;
    border-radius: 8px;
    font-weight: 500;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.3s ease;
    font-family: inherit;

    &:disabled {
        opacity: 0.6;
        cursor: not-allowed;
    }

    // Variants
    &.btn-primary {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;

        &:hover:not(:disabled) {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }
    }

    &.btn-secondary {
        background: transparent;
        color: #667eea;
        border: 2px solid #667eea;

        &:hover:not(:disabled) {
            background: #667eea;
            color: white;
        }
    }

    &.btn-outline {
        background: white;
        color: #333;
        border: 2px solid #e1e5e9;

        &:hover:not(:disabled) {
            border-color: #667eea;
            color: #667eea;
        }
    }

    // Sizes
    &.btn-small {
        padding: 8px 16px;
        font-size: 0.85rem;
    }

    &.btn-medium {
        padding: 12px 24px;
        font-size: 1rem;
    }

    &.btn-large {
        padding: 16px 32px;
        font-size: 1.1rem;
    }

    &.btn-full {
        width: 100%;
    }

    // Loading state
    &.loading {
        .loading-spinner {
            animation: spin 1s linear infinite;
        }
    }
}

@keyframes spin {
    from {
        transform: rotate(0deg);
    }

    to {
        transform: rotate(360deg);
    }
}
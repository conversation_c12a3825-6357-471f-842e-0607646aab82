-- T<PERSON><PERSON> bảng blog_likes
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='blog_likes' AND xtype='U')
CREATE TABLE blog_likes (
    id INT IDENTITY(1,1) PRIMARY KEY,
    blog_id INT NOT NULL,
    user_id INT NOT NULL,
    created_at DATETIME2 DEFAULT GETDATE(),
    CONSTRAINT FK_blog_likes_blog FOREIGN KEY (blog_id) REFERENCES blogs(id) ON DELETE CASCADE,
    CONSTRAINT FK_blog_likes_user FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    CONSTRAINT UQ_blog_likes_unique UNIQUE(blog_id, user_id)
);

-- Tạo bảng blog_comments
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='blog_comments' AND xtype='U')
CREATE TABLE blog_comments (
    id INT IDENTITY(1,1) PRIMARY KEY,
    blog_id INT NOT NULL,
    user_id INT NOT NULL,
    content NVARCHAR(MAX) NOT NULL,
    created_at DATETIME2 DEFAULT GETDATE(),
    updated_at DATETIME2 DEFAULT GETDATE(),
    CONSTRAINT FK_blog_comments_blog FOREIGN KEY (blog_id) REFERENCES blogs(id) ON DELETE CASCADE,
    CONSTRAINT FK_blog_comments_user FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Tạo indexes
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'idx_blog_likes_blog_id')
CREATE INDEX idx_blog_likes_blog_id ON blog_likes(blog_id);

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'idx_blog_likes_user_id')
CREATE INDEX idx_blog_likes_user_id ON blog_likes(user_id);

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'idx_blog_comments_blog_id')
CREATE INDEX idx_blog_comments_blog_id ON blog_comments(blog_id);

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'idx_blog_comments_user_id')
CREATE INDEX idx_blog_comments_user_id ON blog_comments(user_id);

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'idx_blog_comments_created_at')
CREATE INDEX idx_blog_comments_created_at ON blog_comments(created_at DESC);

PRINT 'Đã tạo xong bảng blog_likes và blog_comments'; 
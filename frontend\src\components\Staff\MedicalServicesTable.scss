.medical-services-table {
    padding: 1rem;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    margin-top: 20px;
}

.table-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 1rem;
    flex-wrap: wrap;
    gap: 1rem;
}

.table-header h2 {
    margin: 0;
    font-size: 1.5rem;
    color: #333;
}

.header-actions {
    display: flex;
    align-items: center;
    gap: 1rem;
    flex-wrap: wrap;
}

.search-input {
    padding: 0.5rem 1rem;
    border: 1px solid #ccc;
    border-radius: 4px;
    font-size: 1rem;
    min-width: 260px;
}

.add-button {
    background: #4CAF50;
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 4px;
    cursor: pointer;
    font-size: 1rem;
    font-weight: 500;
    transition: background-color 0.3s;
}

.add-button:hover {
    background: #45a049;
}

.medical-services-table table {
    width: 100%;
    border-collapse: collapse;
    background: #fff;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    border-radius: 8px;
    overflow: hidden;
    table-layout: fixed;
    /* Optional: Uncomment the next line if you want an outer border */
    /* border: 1px solid #e0e0e0; */
}

.medical-services-table th,
.medical-services-table td {
    padding: 0.75rem 1rem;
    border: none; /* Remove all column borders */
    text-align: left;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.medical-services-table th {
    background: #f5f5f5;
    font-weight: 600;
    color: #333;
}

// Cố định kích cỡ các cột
.medical-services-table th:nth-child(1),
.medical-services-table td:nth-child(1) {
    width: 60px;
    min-width: 60px;
    max-width: 60px;
}

.medical-services-table th:nth-child(2),
.medical-services-table td:nth-child(2) {
    width: 200px;
    min-width: 200px;
    max-width: 200px;
}

.medical-services-table th:nth-child(3),
.medical-services-table td:nth-child(3) {
    width: 250px;
    min-width: 250px;
    max-width: 250px;
}

.medical-services-table th:nth-child(4),
.medical-services-table td:nth-child(4) {
    width: 120px;
    min-width: 120px;
    max-width: 120px;
    text-align: center;
}

.medical-services-table th:nth-child(5),
.medical-services-table td:nth-child(5) {
    width: 150px;
    min-width: 150px;
    max-width: 150px;
    text-align: right;
}

.medical-services-table th:nth-child(6),
.medical-services-table td:nth-child(6) {
    width: 120px;
    min-width: 120px;
    max-width: 120px;
    text-align: center;
}

.medical-services-table tr:hover {
    background-color: #f9f9f9;
}

.edit-button,
.delete-button {
    padding: 0.4rem 0.8rem;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.875rem;
    margin-right: 0.5rem;
    transition: background-color 0.3s;
}

.edit-button {
    background: #2196F3;
    color: white;
}

.edit-button:hover {
    background: #1976D2;
}

.delete-button {
    background: #f44336;
    color: white;
}

.delete-button:hover {
    background: #d32f2f;
}

.loading {
    text-align: center;
    padding: 2rem;
    color: #666;
}

.error-message {
    background: #ffebee;
    color: #c62828;
    padding: 1rem;
    border-radius: 4px;
    margin-bottom: 1rem;
}

// Modal styles
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.modal-content {
    background: white;
    border-radius: 8px;
    padding: 0;
    max-width: 500px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem 1.5rem 0 1.5rem;
    border-bottom: 1px solid #e0e0e0;
    margin-bottom: 1.5rem;
}

.modal-header h3 {
    margin: 0;
    color: #333;
    font-size: 1.25rem;
}

.close-button {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: #666;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: background-color 0.3s;
}

.close-button:hover {
    background: #f5f5f5;
    color: #333;
}

.service-form {
    padding: 0 1.5rem 1.5rem 1.5rem;
}

.form-group {
    margin-bottom: 1rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: #333;
}

.form-group input,
.form-group textarea {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid #ccc;
    border-radius: 4px;
    font-size: 1rem;
    box-sizing: border-box;
}

.form-group input:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #2196F3;
    box-shadow: 0 0 0 2px rgba(33, 150, 243, 0.2);
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
}

.form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 1rem;
    margin-top: 2rem;
    padding-top: 1rem;
    border-top: 1px solid #e0e0e0;
}

.cancel-button,
.save-button {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 1rem;
    font-weight: 500;
    transition: background-color 0.3s;
}

.cancel-button {
    background: #f5f5f5;
    color: #333;
}

.cancel-button:hover {
    background: #e0e0e0;
}

.save-button {
    background: #4CAF50;
    color: white;
}

.save-button:hover {
    background: #45a049;
}

// Responsive design
@media (max-width: 768px) {
    .table-header {
        flex-direction: column;
        align-items: stretch;
    }
    
    .header-actions {
        flex-direction: column;
    }
    
    .search-input {
        min-width: auto;
    }
    
    .form-row {
        grid-template-columns: 1fr;
    }
    
    .medical-services-table {
        overflow-x: auto;
    }
    
    .modal-content {
        width: 95%;
        margin: 1rem;
    }
    
    // Reset table layout for mobile
    .medical-services-table table {
        table-layout: auto;
    }
    
    .medical-services-table th,
    .medical-services-table td {
        white-space: normal;
        min-width: auto;
        max-width: none;
    }
} 
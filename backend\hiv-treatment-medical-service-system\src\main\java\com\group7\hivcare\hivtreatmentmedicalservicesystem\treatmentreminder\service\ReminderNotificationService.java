package com.group7.hivcare.hivtreatmentmedicalservicesystem.treatmentreminder.service;

import com.group7.hivcare.hivtreatmentmedicalservicesystem.treatmentreminder.dto.TreatmentReminderDTO;

import java.util.List;

public interface ReminderNotificationService {
    
    /**
     * <PERSON><PERSON><PERSON> thông báo nhắc nhở qua email
     */
    void sendReminderNotification(TreatmentReminderDTO reminder);
    
    /**
     * <PERSON><PERSON><PERSON> thông báo hàng loạt cho các nhắc nhở cần gửi
     */
    void sendBulkReminderNotifications(List<TreatmentReminderDTO> reminders);
    
    /**
     * <PERSON><PERSON><PERSON> thông báo nhắc nhở uống thuốc
     */
    void sendMedicationReminder(TreatmentReminderDTO reminder);
    
    /**
     * <PERSON><PERSON><PERSON> thông báo nhắc nhở tái khám
     */
    void sendAppointmentReminder(TreatmentReminderDTO reminder);
    
    /**
     * <PERSON><PERSON><PERSON> thông báo nhắc nhở bị bỏ lỡ
     */
    void sendMissedReminderNotification(TreatmentReminderDTO reminder);
    
    /**
     * <PERSON><PERSON><PERSON> báo cáo tuân thủ cho bệnh nhân
     */
    void sendComplianceReport(String patientEmail, String patientName, double complianceRate);
} 
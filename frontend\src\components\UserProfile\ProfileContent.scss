.profile-content {
  padding: 30px;
  background: #ffffff;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  align-items: center;

  &__header {
    margin-bottom: 30px;
    border-bottom: 1px solid #e5e5e5;
    padding-bottom: 20px;

    h1 {
      font-size: 28px;
      color: #2c3e50;
      margin: 0 0 12px 0;
      font-weight: 700;
      letter-spacing: -0.5px;
    }

    p {
      color: #7f8c8d;
      margin: 0;
      font-size: 15px;
      line-height: 1.5;
    }
  }

  &__body {
    display: flex;
    gap: 40px;
  }

  &__placeholder {
    text-align: center;
    padding: 60px 20px;
    color: #7f8c8d;
    background: #f8f9fa;
    border-radius: 8px;

    h2 {
      margin-bottom: 12px;
      color: #2c3e50;
      font-weight: 600;
    }
  }
}

.profile-form {
  flex: 1;
  max-width: 700px;

  &__section {
    margin-bottom: 35px;
    background: #ffffff;
    padding: 25px;
    border-radius: 10px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);

    h3 {
      font-size: 20px;
      color: #2c3e50;
      margin: 0 0 25px 0;
      font-weight: 600;
      position: relative;
      padding-bottom: 10px;

      &:after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 0;
        width: 40px;
        height: 3px;
        background: #3498db;
        border-radius: 2px;
      }
    }
  }

  &__grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
    margin-bottom: 0.5rem;

    @media (max-width: 768px) {
      grid-template-columns: 1fr;
      gap: 1rem;
    }
  }

  &__column {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
  }

  &__row {
    margin-bottom: 0.5rem;
  }

  &__field {
    .form-label {
      display: block;
      margin-bottom: 8px;
      font-weight: 500;
      color: #2c3e50;
      font-size: 14px;
    }

    .gender-options {
      display: flex;
      gap: 25px;
      margin-top: 10px;
    }

    .radio-option {
      display: flex;
      align-items: center;
      gap: 8px;
      cursor: pointer;
      font-size: 14px;
      padding: 8px 16px;
      border-radius: 6px;
      transition: all 0.2s ease;

      &:hover {
        background: #f8f9fa;
      }

      input[type="radio"] {
        margin: 0;
        accent-color: #3498db;
      }
    }
  }

  &__actions {
    padding-top: 25px;
    border-top: 1px solid #e5e5e5;
    margin-top: 20px;
  }
}

.profile-avatar {
  width: 200px;
  flex-shrink: 0;

  &__container {
    text-align: center;
    background: #ffffff;
    padding: 25px;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  }

  &__image {
    width: 130px;
    height: 130px;
    border-radius: 50%;
    overflow: hidden;
    margin: 0 auto 25px;
    border: 4px solid #e5e5e5;
    transition: all 0.3s ease;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);

    &:hover {
      transform: scale(1.02);
      border-color: #3498db;
    }

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }

  &__button {
    margin-bottom: 20px;
    width: 100%;
    transition: all 0.2s ease;

    &:hover {
      transform: translateY(-2px);
    }
  }

  &__info {
    p {
      font-size: 13px;
      color: #7f8c8d;
      margin: 6px 0;
      line-height: 1.4;
    }
  }
}

@media (max-width: 768px) {
  .profile-content {
    padding: 20px;
    border-radius: 8px;
    margin-top: 20px;

    &__body {
      flex-direction: column;
      gap: 30px;
    }
  }

  .profile-form {
    max-width: none;

    &__section {
      padding: 20px;
    }
  }

  .profile-avatar {
    width: 100%;
    order: -1;

    &__container {
      display: flex;
      align-items: center;
      gap: 25px;
      text-align: left;
      padding: 20px;
    }

    &__image {
      width: 90px;
      height: 90px;
      margin: 0;
    }

    &__button {
      width: auto;
      margin: 0;
    }
  }
}

.google-profile-style {
  max-width: 700px;
  margin-top: 20px;
  margin: 0 auto;
  padding: 0;
}

.profile-section {
  background: #fff;
  border-radius: 16px;
  border: none;
  box-shadow: none;
  margin-bottom: 32px;
  overflow: hidden;

  &--basic {
    margin-bottom: 32px;
  }

  &__header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 24px 32px 0 32px;
    background: #fafbfc;
    border-bottom: 1px solid #e0e0e0;

    h2 {
      font-size: 1.35rem;
      font-weight: 600;
      margin: 0 0 4px 0;
      color: #222;
    }

    p {
      color: #666;
      font-size: 0.98rem;
      margin: 0;
    }
  }
}

.profile-avatar--google {
  width: 64px;
  height: 64px;
  border-radius: 50%;
  background: #a084e8;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  margin-left: 32px;

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 50%;
  }
}

.profile-table {
  width: 100%;
  display: flex;
  flex-direction: column;
}

.profile-row {
  display: flex;
  align-items: center;
  padding: 0 32px;
  min-height: 56px;
  border-bottom: 1px solid #e0e0e0;
  background: #fff;
  font-size: 1rem;
  transition: background 0.2s;

  &:last-child {
    border-bottom: none;
  }

  &--avatar {
    background: #f5f5f5;
    color: #666;
    font-size: 0.98rem;
  }
}

.profile-label {
  flex: 0 0 180px;
  color: #666;
  font-size: 1rem;
  font-weight: 500;
}

.profile-value {
  flex: 1;
  color: #222;
  font-size: 1.05rem;
  font-weight: 400;
  text-align: left;

  &--avatar {
    color: #888;
    font-size: 0.98rem;
  }
}

.profile-section--contact {
  h2 {
    font-size: 1.2rem;
    font-weight: 600;
    margin: 0;
    padding: 24px 32px 0 32px;
    color: #222;
  }
}

@media (max-width: 768px) {
  .google-profile-style {
    max-width: 100%;
    padding: 0 4px;
  }

  .profile-section__header,
  .profile-row {
    padding: 16px 8px 0 8px;
  }

  .profile-label {
    flex-basis: 110px;
    font-size: 0.98rem;
  }

  .profile-value {
    font-size: 0.98rem;
  }

  .profile-avatar--google {
    width: 48px;
    height: 48px;
    margin-left: 8px;
  }
}

.profile-popup-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.25);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.profile-popup {
  background: #fff;
  border-radius: 16px;
  box-shadow: 0 4px 32px rgba(60, 64, 67, 0.15);
  padding: 32px 32px 24px 32px;
  min-width: 480px;
  max-width: 95vw;
  position: relative;
  font-family: 'Google Sans', Arial, sans-serif;

  h3 {
    margin-top: 0;
    margin-bottom: 1.5rem;
    font-size: 1.25rem;
    font-weight: 600;
    color: #222;
    text-align: center;
  }
}

.profile-popup .popup-input-row input,
.profile-popup .popup-input-row select {
  width: 100%;
  padding: 22px 24px;
  border: 1.5px solid #dadce0;
  border-radius: 12px;
  font-size: 1.35rem;
  background: #fff;
  color: #222;
  transition: border 0.2s, box-shadow 0.2s;
  box-sizing: border-box;
  font-family: inherit;
  font-weight: 400;
}

.profile-popup .popup-input-row input:focus,
.profile-popup .popup-input-row select:focus {
  border-color: #4285f4;
  box-shadow: 0 0 0 2px #e3f0fd;
  outline: none;
}

.profile-popup .popup-actions {
  display: flex;
  justify-content: flex-end;
  gap: 18px;
  margin-top: 24px;
}

.profile-popup .btn {
  min-width: 90px;
  padding: 12px 32px;
  font-size: 1.1rem;
  border-radius: 24px;
  font-weight: 500;
  border: none;
  transition: background 0.18s, color 0.18s, opacity 0.18s;
}

.profile-popup .btn.btn-primary {
  background: #1a73e8;
  color: #ededed;
  cursor: pointer;
}

.profile-popup .btn.btn-primary.enabled {
  background: #1a73e8;
  color: #fff;
  cursor: pointer;
}

.profile-popup .btn.btn-outline {
  background: none;
  color: #1a73e8;
}

.popup-error {
  color: #e74c3c;
  background: #fee2e2;
  border: 1px solid #fecaca;
  border-radius: 6px;
  padding: 0.5rem 0.75rem;
  font-size: 0.95rem;
  margin-bottom: 1rem;
  text-align: center;
}

.profile-popup--confirm {
  min-width: 280px;
  text-align: center;

  p {
    font-size: 1.08rem;
    margin-bottom: 1.5rem;
    color: #000000;
  }
}

.profile-row--clickable {
  transition: background 0.18s;

  &:hover {
    background: #f5f5f5;
  }
}

.message {
  position: fixed;
  top: 20px;
  right: 20px;
  padding: 12px 24px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  z-index: 1000;
  animation: slideIn 0.3s ease-out;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);

  &--success {
    background-color: #4caf50;
    color: white;
  }

  &--error {
    background-color: #f44336;
    color: white;
  }
}

.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.8);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #3498db;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes slideIn {
  from {
    transform: translateX(100%);
    opacity: 0;
  }

  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}
.contact-wrapper {
    padding: 60px 20px;
    max-width: 1200px;
    margin: auto;
    font-family: 'Segoe UI', sans-serif;
}

.contact-header {
    text-align: center;
    margin-bottom: 40px;

    h2 {
        font-size: 2.2rem;
        margin-bottom: 10px;
    }

    p {
        font-size: 1rem;
        color: #666;
    }
}

.contact-body {
    display: flex;
    flex-wrap: wrap;
    gap: 30px;
}

.contact-info {
    flex: 1 1 380px;
    display: flex;
    flex-direction: column;
    gap: 20px;

    .info-block {
        display: flex;
        align-items: flex-start;
        gap: 15px;

        .info-icon {
            font-size: 1.4rem;
            color: #007bff;
            margin-top: 4px;
        }

        h4 {
            margin: 0 0 6px;
        }

        p,
        a {
            margin: 0;
            color: #333;
            text-decoration: none;
        }

        a:hover {
            text-decoration: underline;
            color: #007bff;
        }
    }
}

.contact-form {
    flex: 1 1 580px;
    background: #fff;
    padding: 25px;
    border: 1px solid #eee;
    border-radius: 10px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);

    h4 {
        margin-bottom: 20px;
        font-size: 1.2rem;
    }

    form {
        display: flex;
        flex-direction: column;
        gap: 15px;

        .form-row {
            display: flex;
            gap: 15px;

            input {
                flex: 1;
            }
        }

        input,
        textarea {
            padding: 12px 16px;
            border: 1px solid #ccc;
            border-radius: 8px;
            font-size: 1rem;
            width: 100%;
        }

        textarea {
            resize: vertical;
        }

        button {
            background-color: #000;
            color: #fff;
            border: none;
            padding: 12px 20px;
            font-size: 1rem;
            border-radius: 8px;
            display: flex;
            align-items: center;
            gap: 8px;
            cursor: pointer;
            width: fit-content;
            transition: background 0.3s;

            &:hover {
                background-color: #333;
            }

            .icon {
                font-size: 1rem;
            }
        }
    }
}

@media (max-width: 768px) {
    .contact-body {
        flex-direction: column;
    }

    .form-row {
        flex-direction: column;
    }
}
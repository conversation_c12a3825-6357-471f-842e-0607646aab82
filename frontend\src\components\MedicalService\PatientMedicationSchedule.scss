.patient-medication-schedule {
    padding: 20px;
    background: #ffffff;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

    h3 {
        color: #2c3e50;
        margin-bottom: 20px;
        font-size: 1.5rem;
        font-weight: 600;
    }

    .loading {
        text-align: center;
        padding: 40px;
        color: #666;
        font-style: italic;
    }

    .error-message {
        background: #fee;
        color: #c33;
        padding: 12px;
        border-radius: 4px;
        margin-bottom: 20px;
        border: 1px solid #fcc;
    }

    .success-message {
        background: #efe;
        color: #393;
        padding: 12px;
        border-radius: 4px;
        margin-bottom: 20px;
        border: 1px solid #cfc;
    }

    .no-medications {
        text-align: center;
        padding: 40px;
        color: #666;
        font-style: italic;
        background: #f8f9fa;
        border-radius: 8px;
    }

    .medications-list {
        display: grid;
        gap: 16px;
    }

    .medication-card {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        padding: 20px;
        border: 1px solid #e0e0e0;
        border-radius: 8px;
        background: #fafafa;
        transition: all 0.2s ease;

        &:hover {
            border-color: #007bff;
            background: #f8f9ff;
        }

        .medication-info {
            flex: 1;

            h4 {
                color: #2c3e50;
                margin: 0 0 10px 0;
                font-size: 1.2rem;
                font-weight: 600;
            }

            p {
                margin: 4px 0;
                color: #555;
                font-size: 0.9rem;

                strong {
                    color: #333;
                    font-weight: 600;
                }
            }
        }

        .medication-actions {
            margin-left: 20px;
            display: flex;
            align-items: center;
        }
    }

    /* Modal styles */
    .modal-overlay {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.5);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 1000;
    }

    .modal-content {
        background: white;
        padding: 30px;
        border-radius: 12px;
        width: 90%;
        max-width: 500px;
        max-height: 90vh;
        overflow-y: auto;
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);

        h3 {
            margin: 0 0 20px 0;
            color: #2c3e50;
            font-size: 1.4rem;
            text-align: center;
        }

        .selected-medication {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            border: 1px solid #e9ecef;

            h4 {
                margin: 0 0 8px 0;
                color: #007bff;
                font-size: 1.1rem;
            }

            p {
                margin: 4px 0;
                color: #555;
                font-size: 0.9rem;
            }
        }

        .form-group {
            margin-bottom: 20px;

            label {
                display: block;
                margin-bottom: 8px;
                color: #333;
                font-weight: 600;
                font-size: 0.9rem;
            }

            input {
                width: 100%;
                padding: 12px;
                border: 1px solid #ddd;
                border-radius: 6px;
                font-size: 0.9rem;
                transition: border-color 0.2s ease;

                &:focus {
                    outline: none;
                    border-color: #007bff;
                    box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
                }
            }

            .form-hint {
                display: block;
                margin-top: 6px;
                color: #666;
                font-size: 0.8rem;
                font-style: italic;
            }
        }

        .modal-actions {
            display: flex;
            gap: 12px;
            justify-content: flex-end;
            margin-top: 24px;
            padding-top: 20px;
            border-top: 1px solid #eee;
        }
    }

    /* Responsive */
    @media (max-width: 768px) {
        padding: 16px;

        .medication-card {
            flex-direction: column;
            align-items: stretch;

            .medication-actions {
                margin-left: 0;
                margin-top: 16px;
                justify-content: flex-start;
            }
        }

        .modal-content {
            padding: 20px;
            margin: 20px;
            width: calc(100% - 40px);
        }

        .modal-actions {
            flex-direction: column;
            
            button {
                width: 100%;
            }
        }
    }

    // Time picker styles
    .preset-times {
        display: flex;
        gap: 8px;
        margin-bottom: 16px;
        flex-wrap: wrap;

        .preset-btn {
            background: #e3f2fd;
            color: #1976d2;
            border: 1px solid #bbdefb;
            padding: 6px 12px;
            border-radius: 20px;
            cursor: pointer;
            font-size: 12px;
            transition: all 0.2s;
            
            &:hover {
                background: #1976d2;
                color: white;
            }
        }
    }

    .time-slots {
        display: flex;
        flex-direction: column;
        gap: 12px;
        margin-bottom: 16px;

        .time-slot {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 12px;
            background: #f8f9fa;
            border-radius: 6px;
            border: 1px solid #dee2e6;

            .time-input {
                flex: 1;
                padding: 8px 12px;
                border: 1px solid #ddd;
                border-radius: 4px;
                font-size: 16px;
                background: white;
                
                &:focus {
                    outline: none;
                    border-color: #007bff;
                    box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
                }
            }

            .remove-time-btn {
                background: #dc3545;
                color: white;
                border: none;
                border-radius: 50%;
                width: 24px;
                height: 24px;
                display: flex;
                align-items: center;
                justify-content: center;
                cursor: pointer;
                font-size: 12px;
                transition: background-color 0.2s;

                &:hover {
                    background: #c82333;
                }
            }
        }
    }

    .add-time-btn {
        background: #28a745;
        color: white;
        border: none;
        padding: 8px 16px;
        border-radius: 4px;
        cursor: pointer;
        font-size: 14px;
        margin-bottom: 12px;
        transition: background-color 0.2s;

        &:hover:not(:disabled) {
            background: #218838;
        }

        &:disabled {
            background: #6c757d;
            cursor: not-allowed;
        }
    }

    .preview {
        margin-top: 12px;
        padding: 8px 12px;
        background: #e3f2fd;
        border-radius: 4px;
        border-left: 4px solid #2196f3;
        font-size: 14px;
        color: #1976d2;
    }

    .form-hint {
        color: #6c757d;
        font-size: 12px;
        margin-top: 4px;
        display: block;
    }
}

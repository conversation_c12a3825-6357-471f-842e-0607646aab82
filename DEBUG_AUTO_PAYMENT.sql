-- Debug script để kiểm tra auto payment creation
-- Run this in SQL Server Management Studio or equivalent

-- 1. <PERSON><PERSON><PERSON> tra appointments đã CHECKED_IN nhưng chưa có payment
SELECT 
    'CHECKED_IN appointments without payments:' as [Status];

SELECT 
    a.id as appointment_id, 
    a.appointment_date, 
    a.appointment_time, 
    a.status,
    p.id as patient_id,
    u.full_name as patient_name,
    ms.name as service_name,
    ms.price,
    pay.id as payment_id
FROM appointments a
JOIN patients p ON a.patient_id = p.id
JOIN users u ON p.user_id = u.id
JOIN medical_services ms ON a.medical_service_id = ms.id
LEFT JOIN payments pay ON a.id = pay.appointment_id
WHERE a.status = 'CHECKED_IN' AND pay.id IS NULL;

-- 2. <PERSON><PERSON><PERSON> tra tất cả payments đã tạo
SELECT 
    'All existing payments:' as [Status];

SELECT TOP 10
    p.id as payment_id,
    p.amount,
    p.status,
    p.method,
    p.notes,
    p.payment_date,
    a.id as appointment_id,
    a.status as appointment_status,
    a.appointment_date
FROM payments p
JOIN appointments a ON p.appointment_id = a.id
ORDER BY p.payment_date DESC;

-- 3. <PERSON><PERSON><PERSON> tra appointments có status CONFIRMED để test
SELECT 
    'CONFIRMED appointments that can be checked in:' as [Status];

SELECT TOP 5 
    a.id, 
    a.appointment_date, 
    a.appointment_time, 
    a.status,
    p.id as patient_id,
    u.full_name as patient_name,
    ms.name as service_name,
    ms.price
FROM appointments a
JOIN patients p ON a.patient_id = p.id
JOIN users u ON p.user_id = u.id
JOIN medical_services ms ON a.medical_service_id = ms.id
WHERE a.status = 'CONFIRMED'
ORDER BY a.appointment_date DESC, a.appointment_time DESC;

-- 4. Kiểm tra log table nếu có
SELECT 
    'Recent appointment status changes:' as [Status];

SELECT TOP 10
    id,
    status,
    updated_at,
    notes
FROM appointments 
WHERE status IN ('CHECKED_IN', 'CONFIRMED')
ORDER BY updated_at DESC;

-- 5. Tạo một appointment test để check-in (nếu cần)
/*
DECLARE @PatientId INT = (SELECT TOP 1 id FROM patients);
DECLARE @DoctorId INT = (SELECT TOP 1 id FROM doctors);
DECLARE @ServiceId INT = (SELECT TOP 1 id FROM medical_services);

INSERT INTO appointments (
    patient_id, doctor_id, medical_service_id, 
    appointment_date, appointment_time, status,
    created_at, updated_at
) VALUES (
    @PatientId, @DoctorId, @ServiceId, 
    CAST(GETDATE() AS DATE), 
    CAST('14:00:00' AS TIME), 
    'CONFIRMED',
    GETDATE(), 
    GETDATE()
);

SELECT 'Test appointment created with ID: ' + CAST(SCOPE_IDENTITY() AS VARCHAR(10)) as [Result];
*/

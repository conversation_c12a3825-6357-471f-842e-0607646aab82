/**
 * LUỒNG THANH TOÁN HIỆN TẠI TRONG BACKEND
 * =======================================
 * 
 * Hệ thống thanh toán hỗ trợ 2 phương thức: CASH (tiền mặt) và VNPAY (thanh toán online)
 */

// ==================== ENTITIES ====================

/**
 * 1. PAYMENT (Bảng chính)
 * - id: Primary key
 * - patient_id: FK đến bảng patients
 * - appointment_id: FK đến appointment (OneToOne)
 * - lab_request_id: FK đến lab_request (c<PERSON> thể null)
 * - amount: Số tiền (BigDecimal)
 * - payment_method: CASH hoặc VNPAY
 * - payment_status: PENDING, PAID, FAILED, CANCELLED
 * - payment_date: Thời gian thanh toán
 * - notes: <PERSON><PERSON> chú
 * - transaction_code: Mã giao dịch (dành cho VNPAY)
 */

/**
 * 2. CASH_PAYMENT (Thanh toán tiền mặt)
 * - payment_id: FK đến payment
 * - user_id: FK đến user (staff thực hiện thanh toán)
 * - received_amount: Số tiền nhận được
 * - change_amount: Số tiền thối lại
 */

/**
 * 3. VNPAY_PAYMENT (Thanh toán VNPay)
 * - payment_id: FK đến payment
 * - vnp_txn_ref: Mã tham chiếu giao dịch
 * - vnp_order_info: Thông tin đơn hàng
 * - vnp_response_code: Mã phản hồi từ VNPay
 * - vnp_transaction_no: Số giao dịch VNPay
 * - vnp_bank_code: Mã ngân hàng
 * - vnp_pay_date: Thời gian thanh toán trên VNPay
 * - vnp_card_type: Loại thẻ
 * - gateway_response: Phản hồi từ cổng thanh toán
 */

// ==================== API ENDPOINTS ====================

const paymentAPIs = {
    // STAFF/ADMIN APIs
    getAllPayments: "GET /api/payments", // Lấy tất cả payments (mới thêm)
    createCashPayment: "POST /api/payments/cash", // Tạo thanh toán tiền mặt
    updatePayment: "PUT /api/payments/{id}", // Cập nhật payment
    updatePaymentStatus: "PUT /api/payments/{id}/status", // Cập nhật status
    getPaymentById: "GET /api/payments/{id}", // Lấy payment theo ID
    
    // PATIENT APIs
    createVNPayURL: "POST /api/payments/vnpay", // Tạo URL thanh toán VNPay
    getMyPayments: "GET /api/payments/patient/my-payments", // Lấy payments của bệnh nhân
    getMyPaymentById: "GET /api/payments/patient/{id}", // Lấy payment cụ thể của bệnh nhân
    
    // VNPAY Callback
    vnpayReturn: "GET /api/payments/vnpay/return" // Xử lý callback từ VNPay
};

// ==================== LUỒNG THANH TOÁN TIỀN MẶT ====================

const cashPaymentFlow = {
    step1: "STAFF tạo thanh toán",
    detail1: `
    - POST /api/payments/cash
    - Body: CreatePaymentDTO {
        patientId, appointmentId, amount, method: CASH,
        staffId, notes
    }
    - Staff ID được extract từ JWT token
    `,
    
    step2: "Validation",
    detail2: `
    - Kiểm tra appointment tồn tại
    - Kiểm tra patient tồn tại  
    - Kiểm tra staff tồn tại
    - Kiểm tra appointment chưa có payment
    `,
    
    step3: "Tạo Payment record",
    detail3: `
    - Tạo Payment entity với status PENDING
    - Tạo CashPayment entity liên kết với Staff
    - Lưu vào database
    `,
    
    step4: "Response",
    detail4: `
    - Trả về PaymentDTO với thông tin đầy đủ
    - Log success message
    `
};

// ==================== LUỒNG THANH TOÁN VNPAY ====================

const vnpayPaymentFlow = {
    step1: "PATIENT tạo URL thanh toán",
    detail1: `
    - POST /api/payments/vnpay
    - Body: CreatePaymentDTO {
        patientId, appointmentId, labRequestId(?), 
        amount, method: VNPAY, bankCode(?)
    }
    `,
    
    step2: "Tạo URL VNPay",
    detail2: `
    - Tạo transaction code random 8 chữ số
    - Tạo Payment record với status PENDING
    - Tạo VNPayPayment record
    - Generate VNPay payment URL với các tham số:
      * vnp_TxnRef = transaction_code
      * vnp_Amount = amount * 100
      * vnp_OrderInfo = "Thanh toan lich hen {appointmentId}"
      * vnp_ReturnUrl = /api/payments/vnpay/return
    `,
    
    step3: "PATIENT thanh toán trên VNPay",
    detail3: `
    - Frontend redirect user đến VNPay URL
    - User thực hiện thanh toán trên cổng VNPay
    - VNPay redirect về vnp_ReturnUrl với kết quả
    `,
    
    step4: "Xử lý callback từ VNPay",
    detail4: `
    - GET /api/payments/vnpay/return?vnp_ResponseCode=00&...
    - Verify chữ ký digital từ VNPay
    - Tìm Payment theo vnp_TxnRef (transaction_code)
    - Cập nhật Payment status:
      * vnp_ResponseCode = "00" -> PAID
      * Khác "00" -> FAILED
    - Cập nhật VNPayPayment với thông tin từ VNPay
    `
};

// ==================== PHÂN QUYỀN ====================

const authorization = {
    STAFF: [
        "Xem tất cả payments",
        "Tạo thanh toán tiền mặt", 
        "Cập nhật payment",
        "Xem chi tiết payment"
    ],
    
    ADMIN: [
        "Tất cả quyền của STAFF"
    ],
    
    PATIENT: [
        "Tạo URL thanh toán VNPay",
        "Xem payments của chính mình",
        "Xem chi tiết payment của chính mình"
    ]
};

// ==================== BUSINESS RULES ====================

const businessRules = {
    payment: [
        "Mỗi appointment chỉ có 1 payment (unique constraint)",
        "Payment phải liên kết với patient và appointment",
        "Lab request là optional (có thể null)", 
        "Amount phải > 0",
        "Transaction code là unique cho VNPay"
    ],
    
    cashPayment: [
        "Chỉ STAFF mới có thể tạo cash payment",
        "Staff ID được extract từ JWT, không cho phép fake",
        "Cash payment tự động set status dựa trên input hoặc PENDING"
    ],
    
    vnpayPayment: [
        "Chỉ PATIENT mới có thể tạo VNPay payment",
        "Transaction code tự động generate hoặc dùng input",
        "Status ban đầu luôn là PENDING",
        "Status được update dựa trên callback từ VNPay",
        "Digital signature được verify để đảm bảo tính toàn vẹn"
    ]
};

// ==================== DATABASE RELATIONSHIPS ====================

const relationships = {
    payment: {
        patient: "ManyToOne - Một patient có thể có nhiều payments",
        appointment: "OneToOne - Mỗi appointment chỉ có 1 payment",
        labRequest: "ManyToOne - Một lab request có thể có nhiều payments"
    },
    
    cashPayment: {
        payment: "OneToOne - Mỗi cash payment liên kết với 1 payment",
        user: "ManyToOne - Một staff có thể thực hiện nhiều cash payments"
    },
    
    vnpayPayment: {
        payment: "OneToOne - Mỗi vnpay payment liên kết với 1 payment"
    }
};

// ==================== STATUS WORKFLOW ====================

const statusWorkflow = {
    PENDING: "Thanh toán được tạo, chưa hoàn thành",
    PAID: "Thanh toán thành công",
    FAILED: "Thanh toán thất bại (VNPay error)",
    CANCELLED: "Thanh toán bị hủy (manual action)"
};

console.log("=== PAYMENT SYSTEM OVERVIEW ===");
console.log("API Endpoints:", paymentAPIs);
console.log("Cash Payment Flow:", cashPaymentFlow);
console.log("VNPay Payment Flow:", vnpayPaymentFlow);
console.log("Authorization:", authorization);
console.log("Business Rules:", businessRules);
console.log("Database Relationships:", relationships);
console.log("Status Workflow:", statusWorkflow);

// ==================== RECENT FIXES ====================

const recentFixes = {
    issue1: {
        problem: "500 error 'No static resource api/payments'",
        solution: "Thêm endpoint GET /api/payments cho STAFF/ADMIN",
        implementation: "PaymentController.getAllPayments() + PaymentService.getAllPayments()"
    },
    
    issue2: {
        problem: "404 error 'Không tìm thấy bệnh nhân với ID: 3'",
        solution: "Fix logic tìm Patient trong /api/payments/patient/my-payments",
        implementation: "Dùng patientsRepository.findByUserId(user.getId()) thay vì dùng user.getId() làm patient.getId()"
    },
    
    issue3: {
        problem: "Compilation error với PaymentMapper.toDTO()",
        solution: "Fix method signature - cần 3 tham số (Payment, VNPayPayment, CashPayment)",
        implementation: "Lấy VNPayPayment/CashPayment theo payment.method và gọi mapper.toDTO() đúng cách"
    }
};

console.log("=== RECENT FIXES ===");
console.log(recentFixes);

.lab-results-page {
    background-color: #f9f9f9;
    padding: 2rem 5%;
    font-family: 'Arial', sans-serif;
    margin-top: 80px;

    .back-link {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        color: #555;
        margin-bottom: 2rem;
        cursor: pointer;
    }

    .lab-results-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 2rem;

        h1 {
            font-size: 2rem;
            font-weight: bold;
            margin: 0 0 0.5rem 0;
        }

        p {
            color: #666;
            margin: 0;
        }

        .upload-btn {
            background-color: #333;
            color: #fff;
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: bold;
            display: flex;
            align-items: center;
            gap: 0.5rem;

            &:hover {
                background-color: #555;
            }
        }
    }

    .lab-results-tabs {
        display: flex;
        border-bottom: 1px solid #ddd;
        margin-bottom: 2rem;

        button {
            padding: 1rem 1.5rem;
            border: none;
            background-color: transparent;
            cursor: pointer;
            font-size: 1rem;
            color: #666;
            border-bottom: 3px solid transparent;
            font-weight: 500;
            transition: all 0.3s;

            &.active {
                color: #333;
                font-weight: bold;
                border-bottom-color: #333;
            }
        }
    }

    .lab-results-content {
        display: flex;
        flex-direction: column;
        gap: 1.5rem;
    }

    .results-group {
        background-color: #fff;
        border-radius: 12px;
        padding: 1.5rem 2rem;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);

        &.attention {
            h3 {
                margin-bottom: 0.25rem;
            }

            p {
                color: #666;
                margin-bottom: 1.5rem;
            }
        }

        .group-header {
            display: flex;
            align-items: center;
            gap: 0.75rem;

            .icon {
                font-size: 1.5rem;
                color: #555;
            }

            h3 {
                font-size: 1.25rem;
                margin: 0;
            }
        }

        .group-subtitle {
            color: #666;
            margin: 0.5rem 0 1.5rem 0;
            padding-left: 2.25rem;
        }
    }

    .result-card {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 1.5rem;
        border: 1px solid #eee;
        border-radius: 8px;
        margin-bottom: 1rem;

        &:last-child {
            margin-bottom: 0;
        }

        .result-info {
            h4 {
                margin: 0 0 0.5rem;
                font-size: 1.1rem;
            }

            p {
                margin: 0.25rem 0;
                color: #555;
            }
        }

        .result-actions {
            display: flex;
            align-items: center;
            gap: 1rem;

            .status-tag {
                display: flex;
                align-items: center;
                gap: 0.5rem;
                padding: 0.4rem 0.8rem;
                border-radius: 16px;
                font-size: 0.9rem;
                font-weight: 500;

                &.normal {
                    background-color: #eaf7ed;
                    color: #28a745;
                }
            }

            .details-btn {
                background-color: #fff;
                border: 1px solid #ccc;
                padding: 0.5rem 1rem;
                border-radius: 20px;
                cursor: pointer;
                color: #333;
                font-weight: 500;

                &:hover {
                    background-color: #f0f0f0;
                    border-color: #aaa;
                }
            }
        }

        &.warning {
            background-color: #fffaf0;
            border-left: 4px solid #ffc107;

            .warning-header {
                display: flex;
                align-items: center;
                gap: 0.5rem;
                color: #d97706;
                font-weight: bold;
                font-size: 1.1rem;
                margin-bottom: 0.75rem;
            }

            p {
                font-size: 0.95rem;
            }
        }
    }

    .info-card {
        background-color: #fff;
        border-radius: 12px;
        padding: 1.5rem 2rem;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);

        h3 {
            margin: 0 0 1rem 0;
        }

        p {
            color: #666;
        }

        .info-actions {
            margin-top: 1.5rem;
            display: flex;
            justify-content: flex-end;
            gap: 1rem;
        }

        &.subtle {
            background-color: #f8f9fa;
            display: flex;
            align-items: center;
            gap: 1rem;
            color: #666;
            box-shadow: none;
            border: 1px solid #eee;

            p {
                margin: 0;
            }
        }
    }

    .btn-primary {
        background-color: #333;
        color: #fff;
        padding: 0.75rem 1.5rem;
        border: none;
        border-radius: 8px;
        cursor: pointer;
        font-weight: bold;

        &:hover {
            background-color: #555;
        }
    }

    .btn-secondary {
        background-color: #fff;
        color: #333;
        padding: 0.75rem 1.5rem;
        border: 1px solid #ccc;
        border-radius: 8px;
        cursor: pointer;
        font-weight: bold;

        &:hover {
            background-color: #f0f0f0;
        }
    }
}
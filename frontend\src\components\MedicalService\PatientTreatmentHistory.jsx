import React, { useEffect, useState } from 'react';
import axios from 'axios';
import { FaHistory, FaPills, FaExchangeAlt, FaCalendarAlt, FaUser, FaStethoscope, FaUserMd } from 'react-icons/fa';
import './PatientTreatmentHistory.scss';

const PatientTreatmentHistory = () => {
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);
    const [patientId, setPatientId] = useState(null);
    const [treatmentHistory, setTreatmentHistory] = useState([]);
    const [prescriptionHistory, setPrescriptionHistory] = useState([]);
    const [prescriptionDetails, setPrescriptionDetails] = useState({});
    const [activeTab, setActiveTab] = useState('treatment');
    const [doctorMap, setDoctorMap] = useState({});
    const [protocolMap, setProtocolMap] = useState({});

    useEffect(() => {
        fetchPatientData();
    }, []);

    useEffect(() => {
        if (patientId) {
            fetchHistoryData();
        }
    }, [patientId]);

    const fetchPatientData = async () => {
        try {
            const token = localStorage.getItem('token');
            const response = await axios.get('http://localhost:8080/api/patients/me', {
                headers: { Authorization: `Bearer ${token}` }
            });
            
            if (response.data?.id) {
                setPatientId(response.data.id);
            } else {
                setError('Không thể xác định thông tin bệnh nhân');
            }
        } catch (error) {
            console.error('Error fetching patient data:', error);
            setError('Không thể tải thông tin bệnh nhân');
        }
    };

    const fetchHistoryData = async () => {
        setLoading(true);
        setError(null);

        try {
            const token = localStorage.getItem('token');

            // Fetch supporting data
            const [doctorRes, protocolRes, treatmentHistoryRes, prescriptionHistoryRes] = await Promise.all([
                axios.get('http://localhost:8080/api/doctors', {
                    headers: { Authorization: `Bearer ${token}` }
                }),
                axios.get('http://localhost:8080/api/arv-protocol/active', {
                    headers: { Authorization: `Bearer ${token}` }
                }),
                axios.get(`http://localhost:8080/api/treatment-history/patient/${patientId}`, {
                    headers: { Authorization: `Bearer ${token}` }
                }),
                axios.get(`http://localhost:8080/api/prescription-history/patient/${patientId}`, {
                    headers: { Authorization: `Bearer ${token}` }
                })
            ]);

            // Create doctor map
            const doctorMapObj = {};
            (doctorRes.data || []).forEach(d => {
                doctorMapObj[d.id] = d.fullName || d.username;
            });
            setDoctorMap(doctorMapObj);

            // Create protocol map
            const protocolMapObj = {};
            (protocolRes.data || []).forEach(p => {
                protocolMapObj[p.id] = p.name;
            });
            setProtocolMap(protocolMapObj);

            // Set history data
            setTreatmentHistory(treatmentHistoryRes.data || []);
            setPrescriptionHistory(prescriptionHistoryRes.data || []);

            // Fetch detailed information for prescriptions only
            await fetchPrescriptionDetails(prescriptionHistoryRes.data || []);

        } catch (error) {
            console.error('Error fetching history data:', error);
            setError('Không thể tải lịch sử điều trị');
        } finally {
            setLoading(false);
        }
    };

    const fetchPrescriptionDetails = async (prescriptionHistoryList) => {
        const token = localStorage.getItem('token');
        const details = {};

        for (const history of prescriptionHistoryList) {
            try {
                // Fetch prescription medications using the correct endpoint
                const response = await axios.get(
                    `http://localhost:8080/api/prescription-medications/prescription/${history.prescriptionId}`,
                    { headers: { Authorization: `Bearer ${token}` } }
                );
                details[history.prescriptionId] = response.data || [];
            } catch (error) {
                console.warn(`Could not fetch details for prescription ${history.prescriptionId}:`, error);
                details[history.prescriptionId] = [];
            }
        }

        setPrescriptionDetails(details);
    };

    const formatDate = (dateString) => {
        if (!dateString) return 'N/A';
        return new Date(dateString).toLocaleDateString('vi-VN');
    };

    const formatDateTime = (dateTimeString) => {
        if (!dateTimeString) return 'N/A';
        return new Date(dateTimeString).toLocaleString('vi-VN');
    };

    if (loading) {
        return <div className="treatment-history-loading">Đang tải lịch sử điều trị...</div>;
    }

    if (error) {
        return <div className="treatment-history-error">{error}</div>;
    }

    return (
        <div className="patient-treatment-history">
            <div className="history-header">
                <h2><FaHistory /> Lịch sử điều trị</h2>
                <p>Xem lại các thay đổi trong quá trình điều trị của bạn</p>
            </div>

            <div className="history-tabs">
                <button 
                    className={`tab-button ${activeTab === 'treatment' ? 'active' : ''}`}
                    onClick={() => setActiveTab('treatment')}
                >
                    <FaExchangeAlt /> Lịch sử thay đổi phác đồ
                </button>
                <button 
                    className={`tab-button ${activeTab === 'prescription' ? 'active' : ''}`}
                    onClick={() => setActiveTab('prescription')}
                >
                    <FaPills /> Lịch sử thay đổi đơn thuốc
                </button>
            </div>

            <div className="history-content">
                {activeTab === 'treatment' && (
                    <div className="treatment-history-tab">
                        <h3>Lịch sử thay đổi phác đồ điều trị</h3>
                        {treatmentHistory.length === 0 ? (
                            <div className="no-history">
                                <FaExchangeAlt />
                                <p>Chưa có thay đổi nào trong phác đồ điều trị</p>
                            </div>
                        ) : (
                            <div className="history-list">
                                {treatmentHistory.map((history, index) => (
                                    <div key={index} className="history-item">
                                        <div className="history-timeline">
                                            <div className="timeline-dot"></div>
                                            {index < treatmentHistory.length - 1 && <div className="timeline-line"></div>}
                                        </div>
                                        <div className="history-card">
                                            <div className="history-date">
                                                <FaCalendarAlt /> {formatDateTime(history.createdAt)}
                                            </div>
                                            
                                            <div className="protocol-change">
                                                <div className="protocol-from">
                                                    <span className="label">Phác đồ cũ:</span>
                                                    <span className="value">{history.oldArvProtocolName || protocolMap[history.oldArvProtocolId] || `Protocol ${history.oldArvProtocolId}`}</span>
                                                </div>
                                                <FaExchangeAlt className="arrow-icon" />
                                                <div className="protocol-to">
                                                    <span className="label">Phác đồ mới:</span>
                                                    <span className="value">{history.newArvProtocolName || protocolMap[history.newArvProtocolId] || `Protocol ${history.newArvProtocolId}`}</span>
                                                </div>
                                            </div>

                                            {history.reason && (
                                                <div className="change-reason">
                                                    <span className="label">Lý do thay đổi:</span>
                                                    <span className="value">{history.reason}</span>
                                                </div>
                                            )}

                                            {history.doctorName && (
                                                <div className="doctor-info">
                                                    <span className="label"><FaUserMd /> Bác sĩ thay đổi:</span>
                                                    <span className="value">{history.doctorName}</span>
                                                </div>
                                            )}

                                            {history.notes && (
                                                <div className="history-notes">
                                                    <span className="label">Ghi chú:</span>
                                                    <span className="value">{history.notes}</span>
                                                </div>
                                            )}

                                            <div className="treatment-period">
                                                <span className="label">Ngày bắt đầu:</span>
                                                <span className="value">{formatDate(history.startDate)}</span>
                                            </div>
                                        </div>
                                    </div>
                                ))}
                            </div>
                        )}
                    </div>
                )}

                {activeTab === 'prescription' && (
                    <div className="prescription-history-tab">
                        <h3>Lịch sử thay đổi đơn thuốc</h3>
                        {prescriptionHistory.length === 0 ? (
                            <div className="no-history">
                                <FaPills />
                                <p>Chưa có thay đổi nào trong đơn thuốc</p>
                            </div>
                        ) : (
                            <div className="history-list">
                                {prescriptionHistory.map((history, index) => {
                                    const medications = prescriptionDetails[history.prescriptionId] || [];
                                    return (
                                        <div key={index} className="history-item">
                                            <div className="history-timeline">
                                                <div className="timeline-dot"></div>
                                                {index < prescriptionHistory.length - 1 && <div className="timeline-line"></div>}
                                            </div>
                                            <div className="history-card">
                                                <div className="history-date">
                                                    <FaCalendarAlt /> {formatDateTime(history.modifiedDate || history.createdDate || history.changeDate)}
                                                </div>
                                                
                                                <div className="prescription-info">
                                                    <div className="prescription-id">
                                                        <span className="label">Đơn thuốc:</span>
                                                        <span className="value">#{history.prescriptionId}</span>
                                                    </div>
                                                    {history.doctorId && doctorMap[history.doctorId] && (
                                                        <div className="doctor-info">
                                                            <span className="label">Bác sĩ kê đơn:</span>
                                                            <span className="value">{doctorMap[history.doctorId]}</span>
                                                        </div>
                                                    )}
                                                </div>

                                                {medications.length > 0 ? (
                                                    <div className="prescription-medications">
                                                        <span className="label">Thuốc trong đơn:</span>
                                                        <div className="medications-list">
                                                            {medications.map((med, medIndex) => (
                                                                <div key={medIndex} className="medication-item">
                                                                    <span className="med-name">
                                                                        {med.name || med.medicationName || `Thuốc ID: ${med.medicationId}`}
                                                                    </span>
                                                                    <span className="med-dosage">{med.dosage || 'N/A'}</span>
                                                                    <span className="med-frequency">{med.frequency || 'N/A'}</span>
                                                                    {med.notes && <span className="med-notes">{med.notes}</span>}
                                                                </div>
                                                            ))}
                                                        </div>
                                                    </div>
                                                ) : (
                                                    <div className="no-medications">
                                                        <span className="label">Thuốc trong đơn:</span>
                                                        <span className="value">Không có thông tin thuốc</span>
                                                    </div>
                                                )}

                                                {history.changeReason && (
                                                    <div className="change-reason">
                                                        <span className="label">Lý do thay đổi:</span>
                                                        <span className="value">{history.changeReason}</span>
                                                    </div>
                                                )}

                                                {history.notes && (
                                                    <div className="history-notes">
                                                        <span className="label">Ghi chú:</span>
                                                        <span className="value">{history.notes}</span>
                                                    </div>
                                                )}
                                            </div>
                                        </div>
                                    );
                                })}
                            </div>
                        )}
                    </div>
                )}
            </div>
        </div>
    );
};

export default PatientTreatmentHistory;

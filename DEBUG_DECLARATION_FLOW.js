// Script để test và debug vấn đề declaration data rỗng
console.log(`
=== DEBUG PLAN: Declaration Data Issues ===

🔍 Current Issue:
- Declaration được tạo nhưng các field bị rỗng
- Response có "pregnant": false thay vì "isPregnant": false
- <PERSON><PERSON><PERSON> xác định nguyên nhân: Frontend, Backend, hay Database

📋 Debug Steps:

1. CHECK FRONTEND LOGS:
   - Mở Developer Tools > Console
   - Tạo appointment với declaration
   - Xem các log:
     * "=== DEBUG: AppointmentDeclarationForm submit ==="
     * "=== DEBUG: Frontend payload ==="

2. CHECK BACKEND LOGS:
   - Xem console output của Spring Boot
   - Tìm các log:
     * "=== DEBUG: Incoming request ==="
     * "=== DEBUG: Creating declaration ==="
     * "=== DEBUG: Declaration before save ==="
     * "=== DEBUG: Declaration after save ==="

3. CHECK NETWORK TAB:
   - Request payload gử<PERSON> lên server
   - Response từ server
   - So sánh với expected format

4. EXPECTED FLOW:
   User inputs → Form validation → onSubmit(formData) → 
   setDeclarationData(data) → submitAppointment() → 
   Build payload with declaration data → POST request → 
   Controller receives request → Service processes → 
   Declaration created → Response returned

🎯 Common Issues:

ISSUE 1: Frontend không capture input đúng
→ Check form input binding và handleChange

ISSUE 2: FormData bị reset hoặc override
→ Check state management và form submission

ISSUE 3: Default values override user input
→ Check payload building logic

ISSUE 4: Backend không map đúng fields
→ Check DTO property names và Builder pattern

ISSUE 5: Database constraints hoặc column issues
→ Check table schema và data types

🔧 Quick Fixes:

IF FRONTEND ISSUE:
- Kiểm tra form input values
- Xác nhận formData state updates
- Test form validation

IF BACKEND ISSUE:
- Kiểm tra CreateAppointmentWithDeclarationDTO
- Xác nhận property mapping
- Test request binding

IF DATABASE ISSUE:
- Check column definitions
- Verify save operation
- Test direct SQL insert

📝 Test Payload:

Frontend should send:
{
  "healthNotes": "Sức khỏe tốt",
  "symptoms": "Đau đầu nhẹ", 
  "currentMedications": "Paracetamol",
  "allergies": "Dị ứng tôm",
  "emergencyContact": "Nguyễn Văn A",
  "emergencyPhone": "**********"
}

Backend should receive và save exactly this data.

💡 Next Steps:
1. Test frontend với debug logs
2. Analyze backend debug output
3. Identify where data is lost
4. Apply targeted fix
`);

// Tạo test data mẫu
const sampleDeclarationData = {
    isPregnant: false,
    healthNotes: "Sức khỏe tốt, không có vấn đề gì đặc biệt",
    symptoms: "Đau đầu nhẹ, mệt mỏi", 
    currentMedications: "Paracetamol 500mg, Vitamin C",
    allergies: "Dị ứng tôm, cua và các loại hải sản",
    emergencyContact: "Nguyễn Văn A (chồng)",
    emergencyPhone: "**********"
};

console.log('\n=== SAMPLE TEST DATA ===');
console.log('Paste this into form to test:');
console.log(JSON.stringify(sampleDeclarationData, null, 2));

.profile-content {
    padding: 30px;
    background: #ffffff;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
    align-items: center;
}

.profile-section {
    background: #fff;
    border-radius: 16px;
    border: none;
    box-shadow: none;
    margin-bottom: 32px;
    overflow: hidden;

    &--basic {
        margin-bottom: 32px;
    }

    &__header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 24px 32px 0 32px;
        background: #fafbfc;
        border-bottom: 1px solid #e0e0e0;

        h2 {
            font-size: 1.35rem;
            font-weight: 600;
            margin: 0 0 4px 0;
            color: #222;
        }
    }
}

.profile-actions {
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: 12px;
}

.edit-button,
.change-password-button {
    height: 40px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    font-size: 15px;
    font-weight: 500;
    border-radius: 8px;
    white-space: nowrap;
}

.profile-content .edit-button {
    background-color: #1a73e8 !important;
    color: #fff !important;
    border: none !important;
    padding: 0 28px !important;
    min-width: 150px !important;
    height: 44px !important;
    display: inline-flex !important;
    align-items: center !important;
    justify-content: center !important;
    font-size: 16px !important;
    font-weight: 500 !important;
    border-radius: 8px !important;
    white-space: nowrap !important;
    transition: background 0.2s !important;
    box-sizing: border-box !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
}

.profile-content .edit-button:hover {
    background-color: #1557b0 !important;
}

.change-password-button {
    background-color: white;
    color: #1a73e8;
    border: 1px solid #1a73e8;

    &:hover {
        background-color: #f8f9fa;
    }
}

.profile-form {
    padding: 24px 32px;
}

.form-group {
    margin-bottom: 20px;

    label {
        display: block;
        margin-bottom: 8px;
        font-weight: 500;
        color: #444;
    }

    input,
    select {
        width: 100%;
        padding: 10px 12px;
        border: 1px solid #ddd;
        border-radius: 6px;
        font-size: 14px;
        transition: border-color 0.2s ease;

        &:focus {
            border-color: #1a73e8;
            outline: none;
        }

        &:disabled {
            background-color: #f5f5f5;
            cursor: not-allowed;
        }
    }
}

.form-actions {
    display: flex;
    gap: 12px;
    margin-top: 24px;
}

.save-button,
.cancel-button {
    padding: 10px 20px;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
}

.save-button {
    background-color: #1a73e8;
    color: white;
    border: none;

    &:hover {
        background-color: #1557b0;
    }

    &:disabled {
        background-color: #ccc;
        cursor: not-allowed;
    }
}

.cancel-button {
    background-color: white;
    color: #666;
    border: 1px solid #ddd;

    &:hover {
        background-color: #f8f9fa;
    }
}

.profile-table {
    width: 100%;
}

.profile-row {
    display: flex;
    padding: 16px 32px;
    border-bottom: 1px solid #e0e0e0;

    &:last-child {
        border-bottom: none;
    }
}

.profile-label {
    flex: 0 0 200px;
    color: #666;
    font-weight: 500;
}

.profile-value {
    flex: 1;
    color: #222;
}

.password-form {
    padding: 24px 32px;
}

.password-input-group {
    position: relative;
    display: flex;
    align-items: center;

    input {
        flex: 1;
    }

    .toggle-password {
        position: absolute;
        right: 12px;
        background: none;
        border: none;
        color: #666;
        cursor: pointer;
        padding: 0;
        font-size: 14px;

        &:hover {
            color: #1a73e8;
        }
    }
}

.error-message {
    color: #d93025;
    background-color: #fce8e6;
    padding: 8px 12px;
    border-radius: 4px;
    margin-bottom: 16px;
    font-size: 14px;
}

.success-message {
    color: #137333;
    background-color: #e6f4ea;
    padding: 8px 12px;
    border-radius: 4px;
    margin-bottom: 16px;
    font-size: 14px;
}

.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255, 255, 255, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #1a73e8;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }
}

@media (max-width: 768px) {
    .profile-content {
        padding: 20px;
    }

    .profile-section__header {
        padding: 16px 20px 0 20px;
    }

    .profile-form,
    .password-form {
        padding: 16px 20px;
    }

    .profile-row {
        padding: 12px 20px;
    }

    .profile-label {
        flex: 0 0 140px;
    }
}
# Server Configuration
server.port=8080
server.servlet.encoding.charset=UTF-8
server.servlet.encoding.force=true
server.servlet.encoding.enabled=true

# Database Configuration
spring.application.name=hiv-treatment-medical-service-system
spring.datasource.url=**********************************************************************************************************************************************
spring.datasource.username=sa
spring.datasource.password=12345
spring.datasource.driverClassName=com.microsoft.sqlserver.jdbc.SQLServerDriver

## Hibernate JPA settings
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.SQLServerDialect
spring.jpa.hibernate.ddl-auto=update
spring.jpa.properties.hibernate.format_sql=true
spring.jpa.properties.hibernate.connection.characterEncoding=utf-8
spring.jpa.properties.hibernate.connection.useUnicode=true
spring.jpa.properties.hibernate.connection.CharSet=utf-8

# Allow bean definition overriding
spring.main.allow-bean-definition-overriding=true

# JWT Configuration
jwt.secret=404E635266556A586E3272357538782F413F4428472B4B6250645367566B5970
jwt.expiration=86400000

# Logging Configuration
logging.level.org.springframework.security=DEBUG
logging.level.com.example.hivtreatment=DEBUG


# Email Configuration
spring.mail.host=smtp.gmail.com
spring.mail.port=587
spring.mail.username=<EMAIL>
spring.mail.password=jqis dagz wryl tonj
spring.mail.properties.mail.smtp.auth=true
spring.mail.properties.mail.smtp.starttls.enable=true
spring.mail.properties.mail.smtp.starttls.required=true
spring.mail.properties.mail.smtp.ssl.trust=smtp.gmail.com
spring.mail.default-encoding=UTF-8

# Scheduler Configuration
spring.task.scheduling.pool.size=5

# VNPay Configuration
#vnpay.tmnCode=H0Y32Z9D
#vnpay.hashSecret=XFG2SQLN7R3SO63KH3MNN134L2YCCVL2
#vnpay.payUrl=https://sandbox.vnpayment.vn/paymentv2/vpcpay.html
#vnpay.returnUrl=https://hivcare.com/api/payments/vnpay/return
#vnpay.version=2.1.0
#vnpay.command=pay
#vnpay.currCode=VND
#vnpay.locale=vn
#vnpay.tmn-code=4YUP19I4
#vnpay.secret-key=MDUIFDCRAKLNBPOFIAFNEKFRNMFBYEPX



vnpay.tmn-code=4SDLAN8Q
vnpay.hash-secret=VLYM70J2WUB3DZ64UCGF6EU6F2ZUDSJW
vnpay.pay-url=https://sandbox.vnpayment.vn/paymentv2/vpcpay.html
vnpay.return-url=http://localhost:8080/api/payments/vnpay/return
vnpay.api-url=https://sandbox.vnpayment.vn/merchant_webapi/api/transaction

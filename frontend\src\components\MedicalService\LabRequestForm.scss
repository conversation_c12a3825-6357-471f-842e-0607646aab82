// Modal success overlay styles
.modal-success-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0,0,0,0.25);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-success-box {
  background: #fff;
  border-radius: 18px;
  box-shadow: 0 8px 32px rgba(0,0,0,0.18);
  padding: 36px 36px 28px 36px;
  min-width: 340px;
  max-width: 92vw;
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  animation: popin 0.2s cubic-bezier(.68,-0.55,.27,1.55);
  border-top: 6px solid #52c41a;
}
.modal-success-box.enhanced {
  padding-top: 28px;
}
.modal-success-title {
  font-size: 20px;
  font-weight: 700;
  color: #52c41a;
  margin-bottom: 8px;
  margin-top: 8px;
  text-align: center;
}
.modal-success-icon {
  margin-bottom: 10px;
  margin-top: 4px;
}
.modal-success-message {
  font-size: 16px;
  color: #333;
  font-weight: 500;
  margin-bottom: 10px;
  text-align: center;
  line-height: 1.6;
}
.modal-success-close {
  position: absolute;
  top: 12px;
  right: 12px;
  background: none;
  border: none;
  cursor: pointer;
  padding: 4px;
  border-radius: 50%;
  transition: background 0.15s;
}
.modal-success-close:hover {
  background: #f5f5f5;
}
// Modal error overlay styles
.modal-error-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0,0,0,0.35);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-error-box {
  background: #fff;
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0,0,0,0.18);
  padding: 32px 32px 24px 32px;
  min-width: 320px;
  max-width: 90vw;
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  animation: modalFadeIn 0.25s cubic-bezier(.4,0,.2,1);
}

.modal-error-icon {
  margin-bottom: 12px;
}

.modal-error-message {
  font-size: 18px;
  color: #ff4d4f;
  font-weight: 600;
  margin-bottom: 18px;
  text-align: center;
}

.modal-error-close {
  position: absolute;
  top: 12px;
  right: 12px;
  background: none;
  border: none;
  cursor: pointer;
  padding: 4px;
  border-radius: 50%;
  transition: background 0.15s;
}
.modal-error-box {
  background: #fff;
  border-radius: 18px;
  box-shadow: 0 8px 32px rgba(0,0,0,0.18);
  padding: 36px 36px 28px 36px;
  min-width: 340px;
  max-width: 92vw;
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  animation: modalFadeIn 0.25s cubic-bezier(.4,0,.2,1);
  border-top: 6px solid #ff4d4f;
}
.modal-error-box.enhanced {
  padding-top: 28px;
}
// Modal error title
.modal-error-title {
  font-size: 20px;
  font-weight: 700;
  color: #ff4d4f;
  margin-bottom: 8px;
  margin-top: 8px;
  text-align: center;
}

// Remove stray properties outside selectors (fix SCSS syntax)

// Add popin animation to modal-error-box.enhanced
.modal-error-box.enhanced {
  animation: popin 0.2s cubic-bezier(.68,-0.55,.27,1.55);
}
@keyframes popin {
  0% { transform: scale(0.7); opacity: 0; }
  100% { transform: scale(1); opacity: 1; }
}
.lab-request-form-page {
    min-height: 100vh;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    padding: 2rem 0;

    .form-container {
        max-width: 800px;
        margin: 0 auto;
        background: white;
        border-radius: 16px;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        /* overflow: hidden; */
        position: relative;
    }

    .form-header {
        background: none;
        color: #1f2937;
        padding: 1.5rem 0 1rem 0;
        text-align: center;
        border-bottom: 1px solid #e5e7eb;
        margin-bottom: 1.5rem;

        h1 {
            font-size: 2.1rem;
            font-weight: 800;
            margin-bottom: 0;
            color: #3b2175;
            letter-spacing: -1px;
            font-family: 'Segoe UI', Arial, sans-serif;
        }
    }

    .alert {
        display: flex;
        align-items: center;
        gap: 0.75rem;
        padding: 1rem 2rem;
        margin: 0;
        border-radius: 0;

        &.alert-error {
            background: #fef2f2;
            color: #dc2626;
            border-bottom: 1px solid #fecaca;

            button {
                margin-left: auto;
                background: none;
                border: none;
                color: #dc2626;
                cursor: pointer;
                padding: 0.25rem;
                border-radius: 4px;

                &:hover {
                    background: #fecaca;
                }
            }
        }

        &.alert-success {
            background: #f0fdf4;
            color: #16a34a;
            border-bottom: 1px solid #bbf7d0;
        }
    }

    .lab-request-form {
        padding: 2rem;

        .form-section {
            margin-bottom: 2.5rem;

            h3 {
                font-size: 1.5rem;
                font-weight: 600;
                color: #1f2937;
                margin-bottom: 1.5rem;
                padding-bottom: 0.5rem;
                border-bottom: 2px solid #e5e7eb;
            }
        }

        .form-group {
            margin-bottom: 1.5rem;

            label {
                display: block;
                font-weight: 500;
                color: #374151;
                margin-bottom: 0.5rem;
            }

            select,
            textarea {
                width: 100%;
                padding: 0.75rem;
                border: 2px solid #e5e7eb;
                border-radius: 8px;
                font-size: 1rem;
                transition: border-color 0.2s;

                &:focus {
                    outline: none;
                    border-color: #667eea;
                    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
                }
            }

            textarea {
                resize: vertical;
                min-height: 100px;
            }

            .checkbox-label {
                display: flex;
                align-items: center;
                gap: 0.75rem;
                cursor: pointer;
                font-weight: 500;

                input[type="checkbox"] {
                    width: 1.25rem;
                    height: 1.25rem;
                    accent-color: #667eea;
                }
            }

            .radio-group {
                display: none !important;
            }
            .radio-label {
                display: none !important;
            }
        }

        .test-types-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            grid-auto-rows: minmax(min-content, max-content);
            align-items: stretch;
            gap: 1rem;
        }

        .test-type-card {
            border: 2px solid #e5e7eb;
            border-radius: 10px;
            padding: 0.75rem 1rem;
            cursor: pointer;
            transition: all 0.2s;
            min-height: unset;
            height: auto;
            display: flex;
            flex-direction: column;
            justify-content: flex-start;
            align-items: flex-start;
            overflow: visible;
            font-size: 0.95rem;
            gap: 0.3rem;
            word-break: break-word;
            white-space: normal;
            flex: 1 1 auto;
            width: 100%;

            &:hover {
                border-color: #667eea;
                box-shadow: 0 2px 6px rgba(102, 126, 234, 0.10);
            }

            &.selected {
                border-color: #667eea;
                background: #f8fafc;
                box-shadow: 0 4px 12px rgba(102, 126, 234, 0.2);
            }

            .test-type-header {
                display: flex;
                align-items: center;
                gap: 0.5rem;
                margin-bottom: 0.5rem;

                input[type="checkbox"] {
                    width: 1.1rem;
                    height: 1.1rem;
                    accent-color: #667eea;
                }

                svg {
                    color: #667eea;
                }

                h4 {
                    font-size: 1rem;
                    font-weight: 600;
                    color: #1f2937;
                    margin: 0;
                    line-height: 1.2;
                    word-break: break-word;
                }
            }

            p {
                color: #6b7280;
                margin-bottom: 0.5rem;
                line-height: 1.3;
                word-break: break-word;
                width: 100%;
                font-size: 0.93rem;
            }

            .price {
                display: block;
                font-weight: 600;
                color: #059669;
                font-size: 1rem;
            }
        }

        .form-summary {
            background: #f8fafc;
            border: 2px solid #e5e7eb;
            border-radius: 12px;
            padding: 1.5rem;
            margin-bottom: 2rem;

            .summary-item {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 0.75rem 0;
                border-bottom: 1px solid #e5e7eb;

                &:last-child {
                    border-bottom: none;
                    font-weight: 600;
                    font-size: 1.1rem;

                    .total-price {
                        color: #059669;
                        font-size: 1.2rem;
                    }
                }
            }
        }

        .form-actions {
            display: flex;
            gap: 1rem;
            justify-content: flex-end;

            button {
                padding: 0.75rem 2rem;
                border-radius: 8px;
                font-weight: 500;
                font-size: 1rem;
                cursor: pointer;
                transition: all 0.2s;
                border: none;

                &.btn-secondary {
                    background: #f3f4f6;
                    color: #374151;

                    &:hover {
                        background: #e5e7eb;
                    }
                }

                &.btn-primary {
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    color: white;

                    &:hover:not(:disabled) {
                        transform: translateY(-2px);
                        box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);
                    }

                    &:disabled {
                        opacity: 0.6;
                        cursor: not-allowed;
                    }
                }
            }
        }
    }

    .loading-overlay {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(255, 255, 255, 0.9);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 1000;

        .loading-spinner {
            background: white;
            padding: 2rem;
            border-radius: 12px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            font-weight: 500;
            color: #667eea;
        }
    }
}

// Responsive design
@media (max-width: 768px) {
    .lab-request-form-page {
        padding: 1rem;

        .form-container {
            border-radius: 12px;
        }

        .form-header {
            padding: 1.5rem;

            h1 {
                font-size: 2rem;
            }
        }

        .lab-request-form {
            padding: 1.5rem;

            .test-types-grid {
                grid-template-columns: 1fr;
            }

            .form-actions {
                flex-direction: column;

                button {
                    width: 100%;
                }
            }
        }
    }
}

@media (max-width: 480px) {
    .lab-request-form-page {
        .form-header {
            padding: 1rem;

            h1 {
                font-size: 1.75rem;
            }

            p {
                font-size: 1rem;
            }
        }

        .lab-request-form {
            padding: 1rem;

            .form-section h3 {
                font-size: 1.25rem;
            }
        }
    }
}
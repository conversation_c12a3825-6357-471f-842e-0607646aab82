.appointment-approval-table {
    padding: 1rem;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    margin-top: 20px;
}

.table-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 1rem;
}

.table-header h2 {
    margin: 0;
    font-size: 1.5rem;
}

.search-input {
    padding: 0.5rem 1rem;
    border: 1px solid #ccc;
    border-radius: 4px;
    font-size: 1rem;
    min-width: 260px;
    margin-left: 1rem;
}

.appointment-approval-table table {
    width: 100%;
    border-collapse: collapse;
    background: #fff;
    border-radius: 8px;
    overflow: hidden;
    table-layout: fixed;
    /* Optional: Uncomment the next line if you want an outer border */
    /* border: 1px solid #e0e0e0; */
}

.appointment-approval-table th,
.appointment-approval-table td {
    padding: 0.75rem 1rem;
    border: none;
    /* Remove all column borders */
    text-align: left;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.appointment-approval-table th {
    background: #f5f5f5;
}

// <PERSON><PERSON> định kích cỡ các cột
.appointment-approval-table th:nth-child(1),
.appointment-approval-table td:nth-child(1) {
    width: 60px;
    min-width: 60px;
    max-width: 60px;
}

.appointment-approval-table th:nth-child(2),
.appointment-approval-table td:nth-child(2) {
    width: 150px;
    min-width: 150px;
    max-width: 150px;
}

.appointment-approval-table th:nth-child(3),
.appointment-approval-table td:nth-child(3) {
    width: 150px;
    min-width: 150px;
    max-width: 150px;
}

.appointment-approval-table th:nth-child(4),
.appointment-approval-table td:nth-child(4) {
    width: 180px;
    min-width: 180px;
    max-width: 180px;
}

.appointment-approval-table th:nth-child(5),
.appointment-approval-table td:nth-child(5) {
    width: 120px;
    min-width: 120px;
    max-width: 120px;
    text-align: center;
}

.appointment-approval-table th:nth-child(6),
.appointment-approval-table td:nth-child(6) {
    width: 200px;
    min-width: 200px;
    max-width: 200px;
}

.appointment-approval-table th:nth-child(7),
.appointment-approval-table td:nth-child(7) {
    width: 120px;
    min-width: 120px;
    max-width: 120px;
    text-align: center;
}

.appointment-approval-table th:nth-child(8),
.appointment-approval-table td:nth-child(8) {
    width: 200px;
    min-width: 200px;
    max-width: 200px;
    text-align: center;
}

.approve-btn {
    background: #4caf50;
    color: #fff;
    border: none;
    padding: 0.4rem 0.8rem;
    margin-right: 0.5rem;
    border-radius: 4px;
    cursor: pointer;
}

.edit-btn {
    background: #2196F3;
    color: #fff;
    border: none;
    padding: 0.4rem 0.8rem;
    margin-right: 0.5rem;
    border-radius: 4px;
    cursor: pointer;
    padding-right: 10px;
}

.reject-btn {
    background-color: #f44336;
    color: #fff;
    border: none;
    padding: 0.4rem 0.8rem;
    border-radius: 4px;
    cursor: pointer;

    &:hover {
        background-color: #d32f2f;
    }
}

.approve-btn:hover {
    background: #388e3c;
}

.edit-btn:hover {
    background: #1976D2;
}

.spinner {
    border: 4px solid rgba(0, 0, 0, 0.1);
    width: 24px;
    height: 24px;
    border-radius: 50%;
    border-left-color: #09f;
    margin: 0 auto;
    animation: spin 1s ease infinite;
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }
}

// Responsive design
@media (max-width: 768px) {
    .appointment-approval-table table {
        table-layout: auto;
    }

    .appointment-approval-table th,
    .appointment-approval-table td {
        white-space: normal;
        min-width: auto;
        max-width: none;
    }
}
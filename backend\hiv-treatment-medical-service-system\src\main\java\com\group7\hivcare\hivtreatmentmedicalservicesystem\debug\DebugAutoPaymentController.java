package com.group7.hivcare.hivtreatmentmedicalservicesystem.debug;

import com.group7.hivcare.hivtreatmentmedicalservicesystem.common.entity.*;
import com.group7.hivcare.hivtreatmentmedicalservicesystem.common.repository.*;
import com.group7.hivcare.hivtreatmentmedicalservicesystem.payment.service.PaymentService;
import com.group7.hivcare.hivtreatmentmedicalservicesystem.payment.dto.PaymentDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import org.springframework.http.ResponseEntity;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.LocalTime;
import java.util.List;
import java.util.Optional;
import java.util.HashMap;
import java.util.Map;

@Slf4j
@RestController
@RequestMapping("/api/debug")
@RequiredArgsConstructor
public class DebugAutoPaymentController {

    private final PaymentService paymentService;
    private final AppointmentRepository appointmentRepository;
    private final PaymentRepository paymentRepository;

    @GetMapping("/auto-payment-status")
    public ResponseEntity<?> checkAutoPaymentStatus() {
        log.info("=== Debugging Auto Payment Creation Status ===");
        
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 1. Kiểm tra appointments đã CHECKED_IN
            List<Appointment> checkedInAppointments = appointmentRepository.findAll().stream()
                .filter(apt -> "CHECKED_IN".equals(apt.getStatus()))
                .toList();
                
            result.put("totalCheckedInAppointments", checkedInAppointments.size());
            log.info("Found {} CHECKED_IN appointments", checkedInAppointments.size());
            
            // 2. Kiểm tra số lượng payments
            List<Payment> allPayments = paymentRepository.findAll();
            result.put("totalPayments", allPayments.size());
            log.info("Found {} total payments", allPayments.size());
            
            // 3. Kiểm tra appointments CHECKED_IN không có payment
            int missingPayments = 0;
            for (Appointment appointment : checkedInAppointments) {
                Optional<Payment> payment = paymentRepository.findByAppointmentId(appointment.getId());
                if (payment.isEmpty()) {
                    missingPayments++;
                    log.warn("Appointment ID {} (CHECKED_IN) missing payment", appointment.getId());
                }
            }
            
            result.put("checkedInAppointmentsMissingPayments", missingPayments);
            
            // 4. Kiểm tra auto-created payments
            long autoCreatedPayments = allPayments.stream()
                .filter(p -> p.getNotes() != null && p.getNotes().contains("tự động"))
                .count();
                
            result.put("autoCreatedPayments", autoCreatedPayments);
            log.info("Found {} auto-created payments", autoCreatedPayments);
            
            // 5. Summary
            result.put("autoPaymentWorking", missingPayments == 0);
            result.put("message", missingPayments == 0 ? 
                "✅ Auto payment creation is working correctly" : 
                "❌ Found " + missingPayments + " CHECKED_IN appointments without payments");
            
            log.info("Auto payment status check completed");
            return ResponseEntity.ok(result);
            
        } catch (Exception e) {
            log.error("Error checking auto payment status", e);
            result.put("error", e.getMessage());
            return ResponseEntity.ok(result);
        }
    }
    
    @PostMapping("/test-auto-payment/{appointmentId}")
    @Transactional
    public ResponseEntity<?> testAutoPaymentCreation(@PathVariable Integer appointmentId) {
        log.info("=== Testing Auto Payment Creation for Appointment {} ===", appointmentId);
        
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 1. Kiểm tra appointment tồn tại
            Optional<Appointment> appointmentOpt = appointmentRepository.findById(appointmentId);
            if (appointmentOpt.isEmpty()) {
                result.put("error", "Appointment not found");
                return ResponseEntity.ok(result);
            }
            
            Appointment appointment = appointmentOpt.get();
            result.put("appointmentId", appointmentId);
            result.put("currentStatus", appointment.getStatus());
            
            // 2. Nếu chưa CHECKED_IN, set thành CHECKED_IN
            if (!"CHECKED_IN".equals(appointment.getStatus())) {
                log.info("Changing appointment status from {} to CHECKED_IN", appointment.getStatus());
                appointment.setStatus("CHECKED_IN");
                appointmentRepository.save(appointment);
                result.put("statusChanged", true);
                result.put("newStatus", "CHECKED_IN");
            } else {
                result.put("statusChanged", false);
                log.info("Appointment already CHECKED_IN");
            }
            
            // 3. Kiểm tra payment hiện tại
            Optional<Payment> existingPayment = paymentRepository.findByAppointmentId(appointmentId);
            if (existingPayment.isPresent()) {
                result.put("existingPayment", Map.of(
                    "id", existingPayment.get().getId(),
                    "amount", existingPayment.get().getAmount(),
                    "status", existingPayment.get().getStatus(),
                    "notes", existingPayment.get().getNotes()
                ));
                log.info("Found existing payment ID {}", existingPayment.get().getId());
            } else {
                result.put("existingPayment", null);
                log.info("No existing payment found");
            }
            
            // 4. Test auto payment creation
            try {
                log.info("Attempting to create auto payment...");
                PaymentDTO paymentDTO = paymentService.createAutoPaymentForCheckedInAppointment(appointmentId);
                
                result.put("autoPaymentCreated", true);
                result.put("paymentInfo", Map.of(
                    "id", paymentDTO.getId(),
                    "amount", paymentDTO.getAmount(),
                    "status", paymentDTO.getStatus(),
                    "method", paymentDTO.getMethod(),
                    "notes", paymentDTO.getNotes()
                ));
                
                log.info("✅ Auto payment created successfully: ID {}", paymentDTO.getId());
                
            } catch (Exception e) {
                result.put("autoPaymentCreated", false);
                result.put("autoPaymentError", e.getMessage());
                log.error("❌ Auto payment creation failed: {}", e.getMessage());
            }
            
            return ResponseEntity.ok(result);
            
        } catch (Exception e) {
            log.error("Error during auto payment test", e);
            result.put("error", e.getMessage());
            return ResponseEntity.ok(result);
        }
    }
    
    @GetMapping("/appointments-for-testing")
    public ResponseEntity<?> getAppointmentsForTesting() {
        try {
            List<Appointment> allAppointments = appointmentRepository.findAll();
            
            List<Map<String, Object>> appointmentInfo = allAppointments.stream()
                .limit(10) // Giới hạn 10 appointments đầu tiên
                .map(apt -> {
                    Map<String, Object> info = new HashMap<>();
                    info.put("id", apt.getId());
                    info.put("status", apt.getStatus());
                    info.put("date", apt.getAppointmentDate());
                    info.put("time", apt.getAppointmentTime());
                    info.put("patientId", apt.getPatient().getId());
                    info.put("serviceName", apt.getMedicalService().getName());
                    info.put("servicePrice", apt.getMedicalService().getPrice());
                    
                    // Check if has payment
                    Optional<Payment> payment = paymentRepository.findByAppointmentId(apt.getId());
                    info.put("hasPayment", payment.isPresent());
                    if (payment.isPresent()) {
                        info.put("paymentId", payment.get().getId());
                        info.put("paymentStatus", payment.get().getStatus());
                    }
                    
                    return info;
                })
                .toList();
            
            Map<String, Object> result = new HashMap<>();
            result.put("appointments", appointmentInfo);
            result.put("total", allAppointments.size());
            result.put("message", "Select an appointment ID to test auto payment creation");
            
            return ResponseEntity.ok(result);
            
        } catch (Exception e) {
            log.error("Error getting appointments for testing", e);
            return ResponseEntity.ok(Map.of("error", e.getMessage()));
        }
    }
}

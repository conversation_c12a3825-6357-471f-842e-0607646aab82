// Debug script để test auto payment creation
// Run with: node DEBUG_AUTO_PAYMENT_TEST.js

const http = require('http');

async function testAutoPaymentDebug() {
    console.log('=== Debug Auto Payment Creation ===\n');
    
    // Wait for backend to start
    console.log('Waiting for backend to start...');
    await sleep(10000);
    
    try {
        // 1. Ki<PERSON>m tra status tổng quan
        console.log('1. Checking auto payment status...');
        const statusResult = await makeRequest('/api/debug/auto-payment-status');
        console.log('Status Result:');
        console.log(JSON.stringify(statusResult, null, 2));
        
        console.log('\n' + '='.repeat(50) + '\n');
        
        // 2. <PERSON><PERSON>y danh sách appointments để test
        console.log('2. Getting appointments for testing...');
        const appointmentsResult = await makeRequest('/api/debug/appointments-for-testing');
        console.log('Appointments for testing:');
        
        if (appointmentsResult.appointments && appointmentsResult.appointments.length > 0) {
            appointmentsResult.appointments.slice(0, 5).forEach(apt => {
                console.log(`  - ID: ${apt.id} | Status: ${apt.status} | HasPayment: ${apt.hasPayment} | Service: ${apt.serviceName} | Price: ${apt.servicePrice}`);
            });
            
            // 3. Test với appointment đầu tiên
            const testAppointment = appointmentsResult.appointments[0];
            console.log(`\n3. Testing auto payment creation with appointment ${testAppointment.id}...`);
            
            const testResult = await makeRequest(`/api/debug/test-auto-payment/${testAppointment.id}`, 'POST');
            console.log('Test Result:');
            console.log(JSON.stringify(testResult, null, 2));
            
            // 4. Kiểm tra status lại sau test
            console.log('\n4. Checking status after test...');
            const finalStatus = await makeRequest('/api/debug/auto-payment-status');
            console.log('Final Status:');
            console.log(JSON.stringify(finalStatus, null, 2));
            
        } else {
            console.log('No appointments found to test');
        }
        
    } catch (error) {
        console.error('Error during debug test:', error.message);
    }
}

function makeRequest(path, method = 'GET', data = null) {
    return new Promise((resolve, reject) => {
        const options = {
            hostname: 'localhost',
            port: 8080,
            path: path,
            method: method,
            headers: {
                'Content-Type': 'application/json'
            }
        };
        
        const req = http.request(options, (res) => {
            let responseData = '';
            
            res.on('data', (chunk) => {
                responseData += chunk;
            });
            
            res.on('end', () => {
                try {
                    if (res.statusCode >= 200 && res.statusCode < 300) {
                        const result = responseData ? JSON.parse(responseData) : {};
                        resolve(result);
                    } else {
                        reject(new Error(`HTTP ${res.statusCode}: ${responseData}`));
                    }
                } catch (e) {
                    reject(new Error(`Parse error: ${e.message}`));
                }
            });
        });
        
        req.on('error', (error) => {
            reject(error);
        });
        
        if (data) {
            req.write(JSON.stringify(data));
        }
        
        req.end();
    });
}

function sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
}

// Run the test
console.log('Starting auto payment debug test...');
testAutoPaymentDebug();

// TEST STAFF PAYMENT WORKFLOW
// Staff chỉ có thể tạo payment cho appointments có status CHECKED_IN

const API_BASE = 'http://localhost:8080/api';

// Mock test data
const testStaffCredentials = {
    email: '<EMAIL>',
    password: 'password123'
};

const testData = {
    patientId: 1,
    appointmentId: 1,
    labRequestId: 1,
    amount: 500000,
    method: "CASH",
    status: "PENDING"
};

async function testStaffPaymentWorkflow() {
    try {
        console.log('🧪 TESTING STAFF PAYMENT WORKFLOW');
        console.log('=====================================');

        // 1. Staff login
        console.log('\n1️⃣ Staff Login...');
        const loginResponse = await fetch(`${API_BASE}/auth/login`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(testStaffCredentials)
        });

        if (!loginResponse.ok) {
            throw new Error(`Login failed: ${loginResponse.status}`);
        }

        const loginData = await loginResponse.json();
        const token = loginData.token;
        console.log('✅ Staff login successful');

        const headers = {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
        };

        // 2. Get eligible appointments for payment (CHECKED_IN status, no existing payment)
        console.log('\n2️⃣ Getting eligible appointments for payment...');
        const eligibleResponse = await fetch(`${API_BASE}/payments/eligible-appointments`, {
            headers: headers
        });

        if (!eligibleResponse.ok) {
            throw new Error(`Failed to get eligible appointments: ${eligibleResponse.status}`);
        }

        const eligibleAppointments = await eligibleResponse.json();
        console.log('✅ Eligible appointments retrieved:', eligibleAppointments.length);
        
        if (eligibleAppointments.length > 0) {
            console.log('📋 Sample eligible appointment:', {
                id: eligibleAppointments[0].id,
                patientName: eligibleAppointments[0].patientName,
                doctorName: eligibleAppointments[0].doctorName,
                status: eligibleAppointments[0].status,
                appointmentDate: eligibleAppointments[0].appointmentDate
            });
        } else {
            console.log('⚠️ No eligible appointments found (all appointments either not CHECKED_IN or already have payments)');
        }

        // 3. Test creating payment for CHECKED_IN appointment
        if (eligibleAppointments.length > 0) {
            console.log('\n3️⃣ Creating payment for CHECKED_IN appointment...');
            const eligibleAppointment = eligibleAppointments[0];
            
            const paymentData = {
                patientId: eligibleAppointment.patientId,
                appointmentId: eligibleAppointment.id,
                labRequestId: testData.labRequestId, // This might need to be adjusted based on actual lab requests
                amount: testData.amount,
                method: testData.method,
                status: testData.status
            };

            const createPaymentResponse = await fetch(`${API_BASE}/payments/cash`, {
                method: 'POST',
                headers: headers,
                body: JSON.stringify(paymentData)
            });

            if (createPaymentResponse.ok) {
                const paymentResult = await createPaymentResponse.json();
                console.log('✅ Payment created successfully:', {
                    id: paymentResult.id,
                    amount: paymentResult.amount,
                    method: paymentResult.method,
                    status: paymentResult.status
                });
            } else {
                const errorText = await createPaymentResponse.text();
                console.log('❌ Payment creation failed:', errorText);
            }
        }

        // 4. Test creating payment for non-CHECKED_IN appointment (should fail)
        console.log('\n4️⃣ Testing business rule: Creating payment for non-CHECKED_IN appointment...');
        
        const invalidPaymentData = {
            ...testData,
            appointmentId: 999 // Non-existent or wrong status appointment
        };

        const invalidPaymentResponse = await fetch(`${API_BASE}/payments/cash`, {
            method: 'POST',
            headers: headers,
            body: JSON.stringify(invalidPaymentData)
        });

        if (!invalidPaymentResponse.ok) {
            const errorText = await invalidPaymentResponse.text();
            console.log('✅ Business rule validated - payment creation blocked:', errorText);
        } else {
            console.log('❌ Business rule failed - payment should not be created for non-CHECKED_IN appointment');
        }

        // 5. Get all payments to verify staff can view them
        console.log('\n5️⃣ Getting all payments (staff view)...');
        const allPaymentsResponse = await fetch(`${API_BASE}/payments`, {
            headers: headers
        });

        if (allPaymentsResponse.ok) {
            const allPayments = await allPaymentsResponse.json();
            console.log('✅ All payments retrieved:', allPayments.length);
            
            if (allPayments.length > 0) {
                console.log('💰 Sample payment:', {
                    id: allPayments[0].id,
                    amount: allPayments[0].amount,
                    method: allPayments[0].method,
                    status: allPayments[0].status,
                    patientName: allPayments[0].patientName
                });
            }
        } else {
            console.log('❌ Failed to retrieve all payments');
        }

        console.log('\n🎉 STAFF PAYMENT WORKFLOW TEST COMPLETED');
        console.log('=======================================');

    } catch (error) {
        console.error('🚨 Test failed:', error.message);
    }
}

// Documentation of the workflow
console.log(`
📚 STAFF PAYMENT WORKFLOW DOCUMENTATION
=====================================

Business Rules:
1. Staff can only create payments for appointments with status "CHECKED_IN"
2. Each appointment can only have one payment
3. Staff can view all payments in the system
4. Staff can get list of eligible appointments (CHECKED_IN + no existing payment)

API Endpoints:
- GET /api/payments/eligible-appointments - Get appointments ready for payment
- POST /api/payments/cash - Create cash payment (requires CHECKED_IN status)
- GET /api/payments - Get all payments (staff view)

Expected Workflow:
1. Patient checks in for appointment (status: PENDING → CHECKED_IN)
2. Staff views eligible appointments via /eligible-appointments
3. Staff creates payment for checked-in appointment
4. Payment created with PENDING status initially
5. Staff can update payment status as needed

Error Scenarios:
- Creating payment for non-CHECKED_IN appointment: Returns error
- Creating payment for appointment that already has payment: Returns error
- Creating payment for non-existent appointment: Returns error
`);

// Run the test
testStaffPaymentWorkflow();

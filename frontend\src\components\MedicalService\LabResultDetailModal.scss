.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.6);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
    animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }

    to {
        opacity: 1;
    }
}

.modal-content {
    background-color: #fff;
    border-radius: 12px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
    display: flex;
    flex-direction: column;
    animation: slideIn 0.3s ease-out;
    /* <PERSON><PERSON><PERSON> thước bây giờ đ<PERSON>ợc quản lý bằng inline style trong JSX */
    /* width: 70vw; */
    /* height: 70vh; */
}

@keyframes slideIn {
    from {
        transform: translateY(-30px);
        opacity: 0;
    }

    to {
        transform: translateY(0);
        opacity: 1;
    }
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    padding: 1.5rem;
    border-bottom: 1px solid #eee;

    h3 {
        margin: 0 0 0.25rem 0;
        font-size: 1.5rem;
    }

    p {
        margin: 0;
        color: #666;
    }

    .close-button {
        background: none;
        border: none;
        font-size: 1.5rem;
        color: #888;
        cursor: pointer;
    }
}

.modal-body {
    padding: 1.5rem;
    overflow-y: auto;
    scroll-behavior: smooth;

    .result-summary {
        background-color: #f8f9fa;
        border: 1px solid #eee;
        border-radius: 8px;
        padding: 1.5rem;
        margin-bottom: 2rem;
        display: flex;
        justify-content: space-between;
        align-items: center;

        h4 {
            margin: 0;
            font-size: 1.75rem;
            font-weight: bold;
        }

        p {
            margin: 0 0 0.25rem 0;
            color: #666;
            font-size: 0.9rem;
        }

        .status-tag {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-weight: 500;

            &.attention {
                background-color: #fff3cd;
                color: #856404;
                border: 1px solid #ffeeba;
            }
        }
    }

    .result-details-grid {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 2rem;
        margin-bottom: 2rem;

        .detail-item {
            strong {
                display: block;
                margin-bottom: 1rem;
                font-size: 1.1rem;
            }

            p {
                margin: 0.5rem 0;
                color: #555;
                line-height: 1.6;
            }
        }
    }

    .history-section {
        margin-bottom: 2rem;

        h4 {
            margin-bottom: 1rem;
            font-size: 1.25rem;
        }

        table {
            width: 100%;
            border-collapse: collapse;

            th,
            td {
                padding: 1rem;
                text-align: left;
                border-bottom: 1px solid #eee;
            }

            th {
                background-color: #f1f3f5;
                font-weight: 600;
            }

            tbody tr {
                &:nth-child(odd) {
                    background-color: #f8f9fa;
                }

                &:last-child td {
                    border-bottom: none;
                }
            }
        }
    }

    .important-info {
        background-color: #e9ecef;
        border-radius: 8px;
        padding: 1rem;
        display: flex;
        align-items: center;
        gap: 1rem;
        color: #495057;

        p {
            margin: 0;
            font-size: 0.9rem;
        }
    }
}

.modal-footer {
    display: flex;
    justify-content: flex-end;
    padding: 1.5rem;
    border-top: 1px solid #eee;
    background-color: #f8f9fa;
    gap: 1rem;
}

.btn-tertiary {
    background-color: #fff;
    color: #333;
    padding: 0.75rem 1.5rem;
    border: 1px solid #ccc;
    border-radius: 8px;
    cursor: pointer;
    font-weight: 500;

    &:hover {
        background-color: #e9ecef;
    }
}
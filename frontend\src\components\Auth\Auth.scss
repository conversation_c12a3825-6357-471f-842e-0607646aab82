.auth-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem 1rem;
  background: linear-gradient(135deg, #4a90e2 0%, #7b68ee 100%);
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    width: 200%;
    height: 200%;
    background: url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M11 18c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm48 25c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm-43-7c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm63 31c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM34 90c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm56-76c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM12 86c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm28-65c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm23-11c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-6 60c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm29 22c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zM32 63c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm57-13c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-9-21c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM60 91c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM35 41c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM12 60c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2z' fill='%23ffffff' fill-opacity='0.05' fill-rule='evenodd'/%3E%3C/svg%3E");
    opacity: 0.5;
    animation: backgroundMove 60s linear infinite;
  }
}

@keyframes backgroundMove {
  0% {
    transform: translate(0, 0);
  }

  100% {
    transform: translate(-50%, -50%);
  }
}

.auth-wrapper {
  display: grid;
  grid-template-columns: 1fr;
  max-width: 800px;
  width: 100%;
  background: white;
  border-radius: 24px;
  overflow: hidden;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  position: relative;
  z-index: 1;
  min-height: auto;
}

.auth-card {
  background: white;
  padding: 2.5rem;
  border-radius: 12px;
  width: 100%;
  position: relative;
  overflow: hidden;

  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: linear-gradient(90deg, #4a90e2, #7b68ee);
  }

  &.signup-card {
    max-width: none;
  }
}

.auth-header {
  text-align: center;
  margin-bottom: 2rem;

  .logo {
    margin-bottom: 1.5rem;

    .logo-icon {
      height: 60px;
      width: 70px;
      margin-top: px;
      transition: transform 0.3s ease;

      &:hover {
        transform: scale(1.05);
      }
    }

    .logo-text {
      font-size: 2rem;
      font-weight: 700;
      color: #2c3e50;
      letter-spacing: -0.5px;
    }
  }

  h1 {
    color: #2c3e50;
    margin-bottom: 0.75rem;
    font-size: 1.75rem;
    font-weight: 700;
    letter-spacing: -0.5px;
  }

  p {
    color: #7f8c8d;
    font-size: 0.95rem;
    line-height: 1.5;
    max-width: 400px;
    margin: 0 auto;
  }
}

.auth-form {
  display: flex;
  flex-direction: column;
  gap: 1rem;

  .form-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
    margin-bottom: 0.5rem;

    @media (max-width: 768px) {
      grid-template-columns: 1fr;
      gap: 1rem;
    }
  }

  .form-column {
    display: flex;
    flex-direction: column;
    gap: 1rem;
  }

  .form-row {
    margin-bottom: 0;
  }

  .form-input {
    margin-bottom: 0;

    .form-label {
      display: block;
      margin-bottom: 0.5rem;
      font-weight: 500;
      color: #2c3e50;
      font-size: 0.9rem;
    }

    .input-wrapper {
      position: relative;

      .form-control {
        width: 100%;
        padding: 0.75rem 1rem;
        border: 2px solid #e1e5e9;
        border-radius: 8px;
        font-size: 0.95rem;
        transition: all 0.3s ease;
        background: #f8fafc;
        margin-bottom: 4px;

        &:focus {
          outline: none;
          border-color: #4a90e2;
          box-shadow: 0 0 0 3px rgba(74, 144, 226, 0.1);
          background: white;
        }

        &.error {
          border-color: #e74c3c;
          box-shadow: 0 0 0 3px rgba(231, 76, 60, 0.1);
        }
      }
    }
  }
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  margin: 0.75rem 0;

  .checkbox-container {
    display: flex;
    align-items: flex-start;
    gap: 0.75rem;
    cursor: pointer;

    input[type="checkbox"] {
      width: 18px;
      height: 18px;
      border-radius: 4px;
      border: 2px solid #e1e5e9;
      appearance: none;
      cursor: pointer;
      position: relative;
      transition: all 0.2s ease;
      margin-top: 2px;

      &:checked {
        background: #4a90e2;
        border-color: #4a90e2;

        &::after {
          content: '✓';
          position: absolute;
          color: rgb(77, 101, 238);
          font-size: 12px;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
        }
      }
    }

    .terms-text {
      font-size: 0.9rem;
      color: #4a5568;
      line-height: 1.4;

      .text-link {
        color: #4a90e2;
        text-decoration: none;
        font-weight: 500;

        &:hover {
          text-decoration: underline;
        }
      }
    }
  }
}

.btn-full {
  width: 100%;
  padding: 0.875rem;
  border-radius: 8px;
  font-weight: 600;
  font-size: 0.95rem;
  transition: all 0.3s ease;

  &.primary {
    background: linear-gradient(135deg, #4a90e2 0%, #7b68ee 100%);
    color: white;
    border: none;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(74, 144, 226, 0.2);
    }
  }

  &.outline {
    border: 2px solid #e1e5e9;
    background: white;
    color: #2c3e50;

    &:hover {
      border-color: #4a90e2;
      color: #4a90e2;
    }
  }
}

.social-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.75rem;
  background: white;
  border: 2px solid #e1e5e9;
  padding: 0.875rem;
  border-radius: 8px;
  font-weight: 500;
  color: #2c3e50;
  transition: all 0.3s ease;

  &:hover {
    background: #f8fafc;
    border-color: #4a90e2;
  }

  .google-logo {
    width: 20px;
    height: 20px;
  }
}

.divider {
  display: flex;
  align-items: center;
  text-align: center;
  margin: 1.25rem 0;

  &::before,
  &::after {
    content: '';
    flex: 1;
    border-bottom: 1px solid #e1e5e9;
  }

  span {
    padding: 0 1rem;
    color: #7f8c8d;
    font-size: 0.875rem;
  }
}

.auth-footer {
  text-align: center;
  margin-top: 1.5rem;

  p {
    color: #7f8c8d;
    font-size: 0.95rem;

    .auth-link {
      color: #4a90e2;
      text-decoration: none;
      font-weight: 600;
      margin-left: 0.5rem;
      transition: color 0.2s ease;

      &:hover {
        color: #357abd;
        text-decoration: underline;
      }
    }
  }
}

.auth-image {
  background: linear-gradient(135deg, rgba(74, 144, 226, 0.95) 0%, rgba(123, 104, 238, 0.95) 100%),
    url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="1" fill="white" opacity="0.1"/><circle cx="10" cy="60" r="1" fill="white" opacity="0.1"/><circle cx="90" cy="40" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  position: relative;
  overflow: hidden;

  @media (max-width: 968px) {
    display: none;
  }

  .image-content {
    text-align: center;
    max-width: 400px;
    position: relative;
    z-index: 1;

    h2 {
      font-size: 2.5rem;
      margin-bottom: 1.5rem;
      font-weight: 700;
      line-height: 1.2;
      text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    p {
      font-size: 1.1rem;
      margin-bottom: 2.5rem;
      opacity: 0.9;
      line-height: 1.6;
    }

    .features {
      text-align: left;
      background: rgba(255, 255, 255, 0.1);
      padding: 1.5rem;
      border-radius: 16px;
      backdrop-filter: blur(10px);

      .feature {
        display: flex;
        align-items: center;
        margin-bottom: 1rem;
        font-size: 1rem;

        &:last-child {
          margin-bottom: 0;
        }

        span:first-child {
          background: rgba(255, 255, 255, 0.2);
          width: 28px;
          height: 28px;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-right: 1rem;
          font-size: 0.9rem;
        }
      }
    }
  }
}

// Error message styling
.error-message {
  background: #fee2e2;
  color: #dc2626;
  padding: 0.5rem 0.75rem;
  border-radius: 6px;
  font-size: 0.875rem;
  border: 1px solid #fecaca;
  margin-top: 0.25rem;
}

// Success message styling
.success-message {
  text-align: center;
  padding: 2rem 0;

  .success-icon {
    width: 64px;
    height: 64px;
    background: #10b981;
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    font-weight: bold;
    margin: 0 auto 1.5rem;
    box-shadow: 0 4px 12px rgba(16, 185, 129, 0.2);
  }

  p {
    margin-bottom: 1rem;
    color: #2c3e50;

    &.instruction {
      color: #7f8c8d;
      font-size: 0.95rem;
      line-height: 1.6;
    }
  }

  strong {
    color: #4a90e2;
  }
}

// Responsive adjustments
@media (max-width: 768px) {
  .auth-container {
    padding: 1rem;
  }

  .auth-wrapper {
    border-radius: 16px;
  }

  .auth-card {
    padding: 1.5rem;
  }

  .auth-header {
    h1 {
      font-size: 1.5rem;
    }

    p {
      font-size: 0.9rem;
    }
  }
}

@media (max-width: 480px) {
  .auth-container {
    padding: 0.5rem;
  }

  .auth-card {
    padding: 1.25rem;
  }

  .auth-header h1 {
    font-size: 1.35rem;
  }
}

.login-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background-color: #f5f5f5;
  padding: 20px;

  .login-form {
    background: white;
    padding: 30px;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    width: 100%;
    max-width: 400px;

    h2 {
      text-align: center;
      margin-bottom: 30px;
      color: #333;
    }

    .error-message {
      background-color: #ffebee;
      color: #c62828;
      padding: 10px;
      border-radius: 4px;
      margin-bottom: 20px;
      text-align: center;
    }

    .form-group {
      margin-bottom: 20px;

      input {
        width: 100%;
        padding: 12px;
        border: 1px solid #ddd;
        border-radius: 4px;
        font-size: 16px;
        transition: border-color 0.3s;

        &:focus {
          outline: none;
          border-color: #2196f3;
        }

        &:disabled {
          background-color: #f5f5f5;
          cursor: not-allowed;
        }
      }
    }

    .login-button {
      width: 100%;
      padding: 12px;
      background-color: #2196f3;
      color: white;
      border: none;
      border-radius: 4px;
      font-size: 16px;
      cursor: pointer;
      transition: background-color 0.3s;

      &:hover {
        background-color: #1976d2;
      }

      &:disabled {
        background-color: #90caf9;
        cursor: not-allowed;
      }
    }
  }
}

.logout-button {
  padding: 8px 16px;
  background-color: #dc3545;
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 14px;
  cursor: pointer;
  transition: background-color 0.3s;

  &:hover {
    background-color: #c82333;
  }

  &:active {
    background-color: #bd2130;
  }
}

.login-header {
  display: flex;
  align-items: center;
  gap: 16px;
  justify-content: center;
}

.login-logo {
  height: 48px;
  width: auto;
  object-fit: contain;
}

.login-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: #2d3748;
}
-- Create blog_likes table
CREATE TABLE blog_likes (
    id INT IDENTITY(1,1) PRIMARY KEY,
    blog_id INT NOT NULL,
    user_id INT NOT NULL,
    created_at DATETIME2 DEFAULT GETDATE(),
    FOREI<PERSON><PERSON> KEY (blog_id) REFERENCES blogs(id) ON DELETE CASCADE,
    FOREIG<PERSON> KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE(blog_id, user_id)
);

-- Create blog_comments table
CREATE TABLE blog_comments (
    id INT IDENTITY(1,1) PRIMARY KEY,
    blog_id INT NOT NULL,
    user_id INT NOT NULL,
    content NVARCHAR(MAX) NOT NULL,
    created_at DATETIME2 DEFAULT GETDATE(),
    updated_at DATETIME2 DEFAULT GETDATE(),
    FOREIGN KEY (blog_id) REFERENCES blogs(id) ON DELETE CASCADE,
    <PERSON>OR<PERSON><PERSON><PERSON>Y (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Create indexes for better performance
CREATE INDEX idx_blog_likes_blog_id ON blog_likes(blog_id);
CREATE INDEX idx_blog_likes_user_id ON blog_likes(user_id);
CREATE INDEX idx_blog_comments_blog_id ON blog_comments(blog_id);
CREATE INDEX idx_blog_comments_user_id ON blog_comments(user_id);
CREATE INDEX idx_blog_comments_created_at ON blog_comments(created_at DESC); 
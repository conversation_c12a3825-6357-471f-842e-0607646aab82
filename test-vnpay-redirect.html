<!DOCTYPE html>
<html>
<head>
    <title>Test VNPay Return</title>
</head>
<body>
    <h1>Test VNPay Return Redirect</h1>
    <p>Nhấn v<PERSON><PERSON> liên kết bên dưới để test việc redirect sau khi thanh toán VNPay thành công:</p>
    
    <h2>Test Thanh toán thành công:</h2>
    <a href="http://localhost:5173/profile?paymentSuccess=true&paymentId=3&amount=400000&transactionCode=********&method=VNPAY&status=PAID&appointmentId=1" 
       target="_blank" 
       style="background: #10b981; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; display: inline-block; margin: 10px 0;">
        Test Thanh toán thành công
    </a>
    
    <h2>Test Thanh toán thất bại:</h2>
    <a href="http://localhost:5173/profile?paymentSuccess=false&error=<PERSON>iao dịch bị hủy bỏ" 
       target="_blank" 
       style="background: #dc2626; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; display: inline-block; margin: 10px 0;">
        Test Thanh toán thất bại
    </a>
    
    <h2>Test URL thực tế từ VNPay:</h2>
    <p><strong>URL callback từ VNPay sẽ được redirect từ:</strong></p>
    <code style="background: #f3f4f6; padding: 10px; display: block; margin: 10px 0; word-break: break-all;">
        http://localhost:8080/api/payments/vnpay/return?vnp_Amount=********&vnp_BankCode=NCB&vnp_BankTranNo=VNP********&vnp_CardType=ATM&vnp_OrderInfo=Thanh+toan+lich+hen+********&vnp_PayDate=**************&vnp_ResponseCode=00&vnp_TmnCode=4SDLAN8Q&vnp_TransactionNo=********&vnp_TransactionStatus=00&vnp_TxnRef=********&vnp_SecureHash=76245d7b2da6db6a9c8970988a5d8fbae9a8b85444a17cf47a0e6041fb047867b5676b38bf5f2160c635038f08df065d7ad2ef6eefa1fb065631c8cfb02ddbae
    </code>
    
    <p><strong>Tới URL frontend:</strong></p>
    <code style="background: #f3f4f6; padding: 10px; display: block; margin: 10px 0; word-break: break-all;">
        http://localhost:5173/profile?paymentSuccess=true&paymentId=[ID]&amount=[AMOUNT]&transactionCode=[CODE]&method=VNPAY&status=PAID&appointmentId=[APPOINTMENT_ID]
    </code>
    
    <h2>Hướng dẫn test:</h2>
    <ol>
        <li>Đảm bảo frontend React đang chạy ở port 5173</li>
        <li>Đảm bảo backend Spring Boot đang chạy ở port 8080</li>
        <li>Nhấn vào link test ở trên để xem modal thanh toán thành công</li>
        <li>Kiểm tra xem modal có hiển thị đúng thông tin không</li>
        <li>Kiểm tra URL có được clean sau khi đóng modal không</li>
    </ol>
</body>
</html>

// Base container for the view
.doctor-lab-request-view {
    padding: 1rem;
    background: #f9fafb; // A slightly off-white background
    border-radius: 8px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
    margin-top: 20px;
    font-family: 'Segoe UI', '<PERSON><PERSON>', '<PERSON><PERSON>', sans-serif;

    // Header section with Title, Search, and Button
    .table-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 1.5rem;

        h2 {
            margin: 0;
            font-size: 1.75rem;
            font-weight: 600;
            color: #111827;
        }

        .header-actions {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .search-input {
            padding: 0.5rem 1rem;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            font-size: 1rem;
            min-width: 280px;
            transition: border-color 0.2s, box-shadow 0.2s;

            &:focus {
                outline: none;
                border-color: #3b82f6;
                box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.2);
            }
        }

        .add-button {
            background: #2563eb;
            color: white;
            border: none;
            border-radius: 6px;
            padding: 0.6rem 1.2rem;
            font-size: 1rem;
            font-weight: 500;
            cursor: pointer;
            transition: background-color 0.2s;

            &:hover {
                background: #1d4ed8;
            }
        }
    }

    // Tabs for filtering
    .tabs {
        display: flex;
        gap: 0.5rem;
        margin-bottom: 1rem;
        border-bottom: 1px solid #e5e7eb;

        .tab-btn {
            background: none;
            border: none;
            padding: 0.75rem 1rem;
            font-size: 1rem;
            font-weight: 500;
            color: #6b7280;
            cursor: pointer;
            border-bottom: 2px solid transparent;
            transition: color 0.2s, border-color 0.2s;

            &.active,
            &:hover {
                color: #1d4ed8;
            }

            &.active {
                border-bottom-color: #1d4ed8;
            }
        }
    }

    // Table styles
    .table-wrapper {
        background: #fff;
        border: 1px solid #e5e7eb;
        border-radius: 8px;
        overflow: hidden;

        table {
            width: 100%;
            border-collapse: collapse;

            th,
            td {
                padding: 0.75rem 1.25rem;
                text-align: left;
                vertical-align: middle;
            }

            thead {
                background-color: #f9fafb;

                tr {
                    th {
                        color: #4b5563;
                        font-weight: 600;
                        font-size: 0.875rem;
                        text-transform: uppercase;
                        letter-spacing: 0.05em;
                    }
                }
            }

            tbody {
                tr {
                    border-bottom: 1px solid #e5e7eb;

                    &:last-child {
                        border-bottom: none;
                    }
                }

                td {
                    color: #374151;
                }
            }
        }
    }

    // Status pill styles
    .status-pill {
        display: inline-block;
        padding: 0.25rem 0.75rem;
        border-radius: 9999px;
        font-size: 0.875rem;
        font-weight: 500;
        text-transform: capitalize;

        &.pending {
            background-color: #fef3c7;
            color: #b45309;
        }

        &.completed {
            background-color: #d1fae5;
            color: #065f46;
        }
    }

    // Action button in table
    .action-btn {
        background: none;
        border: none;
        padding: 6px;
        border-radius: 50%;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: background 0.18s;
        color: #6b7280;
        font-size: 18px;

        &:hover {
            background: #f3f4f6;
            color: #111827;
        }
    }

    // Modal styles (moved from DoctorAppointmentTable.scss)
    .modal-content {
        font-family: 'Segoe UI', 'Roboto', 'Arial', sans-serif;
        background: #fff;
        border-radius: 14px;
        box-shadow: 0 4px 32px rgba(0, 0, 0, 0.15);
        padding: 36px 32px 28px 32px;
        min-width: 350px;
        max-width: 500px;

        h2.lab-request-title {
            text-align: center;
            font-size: 1.5rem;
            font-weight: 700;
            color: #374151;
            margin-bottom: 32px;
        }

        .form-group {
            margin-bottom: 20px;
            display: flex;
            flex-direction: column;
            align-items: flex-start;

            label {
                font-weight: 600;
                color: #374151;
                margin-bottom: 8px;
                font-size: 1.05rem;
            }

            select,
            textarea,
            input[type="text"] {
                width: 100%;
                padding: 10px 12px;
                border: 1.5px solid #bbb;
                border-radius: 6px;
                font-size: 1rem;
                font-family: inherit;
                box-sizing: border-box;
                transition: border-color 0.2s;

                &:focus {
                    outline: none;
                    border-color: #1a73e8;
                    box-shadow: 0 0 3px rgba(26, 115, 232, 0.15);
                }
            }
        }

        .urgent-row {
            flex-direction: row;
            align-items: center;
            gap: 8px;
        }

        .testtype-row {
            display: flex;
            flex-direction: row;
            align-items: center;
            gap: 10px;

            .testtype-note-input {
                flex-grow: 1;
            }
        }

        .selected-testtypes-list {
            display: flex;
            flex-direction: column;
            gap: 8px;
            margin-top: 8px;
            margin-bottom: 20px;
            max-height: 150px;
            overflow-y: auto;
            padding-right: 5px;
        }

        .selected-test-type-chip {
            background: #e3f0fd;
            border-radius: 8px;
            padding: 8px 12px;
            display: flex;
            align-items: center;
            gap: 10px;
            font-size: 0.98rem;

            .chip-type-name {
                font-weight: 500;
                color: #1d4ed8;
                flex-grow: 1;
            }

            .chip-note-input {
                flex-basis: 50%;
            }

            .chip-remove-btn {
                background: none;
                border: none;
                color: #900;
                font-weight: bold;
                cursor: pointer;
                font-size: 20px;
                padding: 0 4px;
                line-height: 1;

                &:hover {
                    color: #d00;
                }
            }
        }

        .empty-chip {
            color: #6b7280;
            padding: 10px;
            text-align: center;
            border: 1px dashed #d1d5db;
            border-radius: 8px;
        }


        .form-actions {
            display: flex;
            justify-content: flex-end;
            gap: 1rem;
            margin-top: 24px;
            padding-top: 20px;
            border-top: 1px solid #e5e7eb;
        }

        .cancel-button,
        .save-button {
            padding: 10px 24px;
            border-radius: 8px;
            font-size: 1rem;
            font-weight: 500;
            cursor: pointer;
            border: 1px solid transparent;
            transition: all 0.2s;
        }

        .cancel-button {
            background-color: #fff;
            color: #374151;
            border-color: #d1d5db;

            &:hover {
                background-color: #f9fafb;
            }
        }

        .save-button {
            background-color: #1a73e8;
            color: #fff;

            &:hover {
                background-color: #1557b0;
            }

            &:disabled {
                background: #a5b4fc;
                cursor: not-allowed;
            }
        }
    }
}

.modal-content.lab-request-modal {
    padding: 36px 32px 28px 32px;
    min-width: 350px;
    max-width: 500px;
    background: #fff;
    border-radius: 16px;
    box-shadow: 0 4px 32px rgba(0, 0, 0, 0.15);
}

.lab-request-form {
    display: flex;
    flex-direction: column;
    gap: 18px;
}

.lab-request-form .form-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.lab-request-form label {
    font-weight: 600;
    color: #374151;
    margin-bottom: 4px;
}

.lab-request-form select,
.lab-request-form input[type="text"],
.lab-request-form input[type="checkbox"] {
    border: 1px solid #d1d5db;
    border-radius: 8px;
    padding: 10px 14px;
    font-size: 1rem;
    transition: border-color 0.2s, box-shadow 0.2s;
    outline: none;
}

.lab-request-form select:focus,
.lab-request-form input[type="text"]:focus {
    border-color: #2563eb;
    box-shadow: 0 0 0 2px rgba(37, 99, 235, 0.15);
}

.lab-request-form .testtype-row {
    display: flex;
    gap: 10px;
    align-items: center;
}

.lab-request-form .testtype-row select,
.lab-request-form .testtype-row input[type="text"] {
    flex: 1;
}

.lab-request-form .testtype-add-btn {
    background: #2563eb;
    color: #fff;
    border: none;
    border-radius: 8px;
    padding: 10px 18px;
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    transition: background 0.2s;
}

.lab-request-form .testtype-add-btn:disabled {
    background: #bcd0fa;
    cursor: not-allowed;
}

.lab-request-form .testtype-add-btn:hover:not(:disabled) {
    background: #1d4ed8;
}

.selected-testtypes-list {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-bottom: 8px;
}

.selected-test-type-chip {
    display: flex;
    align-items: center;
    background: #f3f6fd;
    border: 1px solid #dbeafe;
    border-radius: 999px;
    padding: 6px 14px 6px 10px;
    font-size: 0.98rem;
    gap: 8px;
    box-shadow: 0 1px 4px rgba(37, 99, 235, 0.04);
}

.selected-test-type-chip .chip-type-name {
    font-weight: 500;
    color: #2563eb;
}

.selected-test-type-chip .chip-note-input {
    border: none;
    background: transparent;
    border-bottom: 1px solid #bcd0fa;
    border-radius: 0;
    font-size: 0.98rem;
    padding: 2px 4px;
    min-width: 60px;
}

.selected-test-type-chip .chip-remove-btn {
    background: none;
    border: none;
    color: #ef4444;
    font-size: 1.2rem;
    cursor: pointer;
    margin-left: 4px;
    transition: color 0.18s;
}

.selected-test-type-chip .chip-remove-btn:hover {
    color: #b91c1c;
}

.empty-chip {
    color: #888;
    font-style: italic;
    padding: 4px 0;
}

.lab-request-form .form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    margin-top: 8px;
}

.lab-request-form .cancel-button {
    background: #f3f4f6;
    color: #374151;
    border: none;
    border-radius: 8px;
    padding: 10px 18px;
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    transition: background 0.2s;
}

.lab-request-form .cancel-button:hover {
    background: #e5e7eb;
}

.lab-request-form .save-button {
    background: #2563eb;
    color: #fff;
    border: none;
    border-radius: 8px;
    padding: 10px 18px;
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    transition: background 0.2s;
}

.lab-request-form .save-button:disabled {
    background: #bcd0fa;
    cursor: not-allowed;
}

.lab-request-form .save-button:hover:not(:disabled) {
    background: #1d4ed8;
}

.alert {
    padding: 10px 16px;
    border-radius: 8px;
    font-size: 1rem;
    margin-bottom: 8px;
    text-align: center;
}

.alert.error {
    background: #fee2e2;
    color: #b91c1c;
    border: 1px solid #fecaca;
}

.alert.success {
    background: #d1fae5;
    color: #065f46;
    border: 1px solid #6ee7b7;
}

@media (max-width: 600px) {
    .modal-content.lab-request-modal {
        padding: 18px 6px 18px 6px;
        min-width: unset;
        max-width: 98vw;
    }

    .lab-request-form .form-actions {
        flex-direction: column;
        gap: 8px;
    }

    .lab-request-form .testtype-row {
        flex-direction: column;
        gap: 8px;
        align-items: stretch;
    }

    .selected-testtypes-list {
        flex-direction: column;
        gap: 6px;
    }
}

.switch {
    position: relative;
    display: inline-block;
    width: 44px;
    height: 24px;
    margin-right: 12px;
}

.switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #d1d5db;
    border-radius: 24px;
    transition: .3s;
}

.switch input:checked+.slider {
    background-color: #2563eb;
}

.slider:before {
    position: absolute;
    content: "";
    height: 18px;
    width: 18px;
    left: 3px;
    bottom: 3px;
    background-color: white;
    border-radius: 50%;
    transition: .3s;
}

.switch input:checked+.slider:before {
    transform: translateX(20px);
}

.urgent-label {
    font-weight: 500;
    color: #374151;
    margin-left: 8px;
    font-size: 1rem;
}
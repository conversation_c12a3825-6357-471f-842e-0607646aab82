.form-input {
    margin-bottom: 20px;

    .form-label {
        display: block;
        margin-bottom: 8px;
        font-weight: 500;
        color: #333;
        font-size: 0.9rem;
    }

    .input-wrapper {
        position: relative;
        display: flex;
        align-items: center;

        .input-icon {
            position: absolute;
            left: 12px;
            color: #666;
            font-size: 1.1rem;
            z-index: 1;
        }

        .date-input-wrapper {
            position: relative;
            width: 100%;

            input[type="date"] {
                width: 100%;
                padding: 12px 16px;
                border: 2px solid #e1e5e9;
                border-radius: 8px;
                font-size: 1rem;
                transition: all 0.3s ease;
                background: white;
                color: #000;
                position: relative;
                z-index: 1;

                &::-webkit-calendar-picker-indicator {
                    cursor: pointer;
                    position: relative;
                    z-index: 2;
                }

                &:focus {
                    outline: none;
                    border-color: #667eea;
                    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
                }

                &.error {
                    border-color: #e74c3c;
                    box-shadow: 0 0 0 3px rgba(231, 76, 60, 0.1);
                }
            }

            .date-placeholder {
                position: absolute;
                left: 16px;
                top: 50%;
                transform: translateY(-50%);
                color: #666;
                pointer-events: none;
                z-index: 0;
            }
        }

        .form-control {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 1rem;
            transition: all 0.3s ease;
            background: white;
            color: #000;

            &.with-icon {
                padding-left: 45px;
            }

            &:focus {
                outline: none;
                border-color: #667eea;
                box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
            }

            &.error {
                border-color: #e74c3c;
                box-shadow: 0 0 0 3px rgba(231, 76, 60, 0.1);
            }

            &::placeholder {
                color: #666;
            }
        }

        .password-toggle {
            position: absolute;
            right: 12px;
            background: none;
            border: none;
            cursor: pointer;
            font-size: 1.1rem;
            color: #000000;
            padding: 4px;
            border-radius: 4px;
            transition: background 0.3s ease;

            &:hover {
                background: rgba(0, 0, 0, 0.05);
            }
        }
    }

    .error-message {
        display: block;
        margin-top: 6px;
        color: #e74c3c;
        font-size: 0.85rem;
    }
}
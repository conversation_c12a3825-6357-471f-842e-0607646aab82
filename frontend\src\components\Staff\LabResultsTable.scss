.lab-results-table {
    padding: 1rem;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    margin-top: 20px;
}

.table-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 1rem;
}

.table-header h2 {
    margin: 0;
    font-size: 1.5rem;
}

.search-input {
    padding: 0.5rem 1rem;
    border: 1px solid #ccc;
    border-radius: 4px;
    font-size: 1rem;
    min-width: 260px;
    margin-left: 1rem;
}

.lab-results-table table {
    width: 100%;
    border-collapse: collapse;
    background: #fff;
    border-radius: 8px;
    overflow: hidden;
    table-layout: fixed;
    /* Optional: Uncomment the next line if you want an outer border */
    /* border: 1px solid #e0e0e0; */
}

.lab-results-table th,
.lab-results-table td {
    padding: 0.75rem 1rem;
    border: none; /* Remove all column borders */
    text-align: left;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.lab-results-table th {
    background: #f5f5f5;
}

.doctor-note {
    background: #e3f2fd;
    color: #1976d2;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-size: 0.875rem;
    font-weight: 500;
}

.no-note {
    color: #999;
    font-style: italic;
    font-size: 0.875rem;
}

.add-btn {
    background: #2196F3;
    color: #fff;
    border: none;
    // padding: 0.4rem 0.8rem;
    margin-right: 0.5rem;
    border-radius: 4px;
    cursor: pointer;
}

.download-btn {
    background: #4caf50;
    color: #fff;
    border: none;
    padding: 0.4rem 0.8rem;
    border-radius: 4px;
    cursor: pointer;
}

.view-btn:hover {
    background: #1976D2;
}

.download-btn:hover {
    background: #388e3c;
}
// Test lấy khai báo y tế mới nhất của bệnh nhân
const API_BASE = 'http://localhost:8080/api';

async function testLatestPatientDeclaration() {
    try {
        console.log('🩺 Testing Latest Patient Medical Declaration...');

        // Test lấy khai báo mới nhất của bệnh nhân
        console.log('\n=== Test: Lấy khai báo mới nhất của bệnh nhân ===');
        
        const patientId = 1;
        
        const patientDeclarationResponse = await fetch(`${API_BASE}/appointment-declarations/patient/${patientId}`, {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json'
            }
        });

        console.log('📡 Response status:', patientDeclarationResponse.status);
        
        if (patientDeclarationResponse.ok) {
            const declarations = await patientDeclarationResponse.json();
            console.log('✅ Tất cả khai báo của patient', patientId, ':', declarations);
            console.log('📊 Số lượng khai báo:', declarations?.length || 0);
            
            if (declarations && declarations.length > 0) {
                console.log('\n📋 Khai báo mới nhất (sẽ hiển thị cho bác sĩ):');
                const latest = declarations[0];
                console.log('   - ID:', latest.id);
                console.log('   - Appointment ID:', latest.appointmentId);
                console.log('   - Ngày tạo:', latest.createdAt);
                console.log('   - Mang thai:', latest.isPregnant ? 'Có' : 'Không');
                console.log('   - Triệu chứng:', latest.symptoms || 'Không có');
                console.log('   - Ghi chú sức khỏe:', latest.healthNotes || 'Không có');
                console.log('   - Thuốc đang dùng:', latest.currentMedications || 'Không có');
                console.log('   - Dị ứng:', latest.allergies || 'Không có');
                console.log('   - Liên hệ khẩn cấp:', latest.emergencyContact || 'Không có');
                console.log('   - SĐT khẩn cấp:', latest.emergencyPhone || 'Không có');
            } else {
                console.log('ℹ️ Bệnh nhân chưa có khai báo y tế nào');
            }
        } else if (patientDeclarationResponse.status === 403) {
            console.log('🔒 Cần authentication để test API này');
            console.log('📝 Luồng mới:');
            console.log('   1. Component PatientMedicalDeclaration chỉ cần patientId');
            console.log('   2. Tự động lấy khai báo mới nhất của bệnh nhân');
            console.log('   3. Hiển thị trong PatientDetailModal');
            console.log('   4. Không phụ thuộc vào appointmentId cụ thể');
        } else if (patientDeclarationResponse.status === 404) {
            console.log('ℹ️ Không tìm thấy khai báo nào cho bệnh nhân này');
        } else {
            const error = await patientDeclarationResponse.text();
            console.log('❌ Lỗi:', error);
        }

        console.log('\n=== Thay đổi đã thực hiện ===');
        console.log('🔄 PatientMedicalDeclaration.jsx:');
        console.log('   ✅ Bỏ dependency vào appointmentId');
        console.log('   ✅ Luôn lấy khai báo mới nhất của bệnh nhân');
        console.log('   ✅ Sử dụng getDeclarationsByPatientId() từ backend');
        console.log('   ✅ Lấy declarations[0] (mới nhất)');
        
        console.log('\n🎨 PatientDetailModal.jsx:');
        console.log('   ✅ Chỉ truyền patientId cho component');
        console.log('   ✅ Bỏ appointmentId dependency');
        
        console.log('\n📊 Backend query:');
        console.log('   ✅ ORDER BY appointment_date DESC');
        console.log('   ✅ Khai báo mới nhất sẽ ở đầu array');

        console.log('\n🎯 Kết quả:');
        console.log('   ✅ Bác sĩ luôn thấy khai báo y tế mới nhất');
        console.log('   ✅ Không còn lỗi 404 cho appointment cụ thể');
        console.log('   ✅ Hiển thị thông tin đầy đủ và cập nhật');

    } catch (error) {
        console.log('❌ Network error:', error.message);
    }
}

// Chạy test
testLatestPatientDeclaration();

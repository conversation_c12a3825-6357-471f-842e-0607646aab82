.patient-list-view {
    padding: 1.5rem;
    font-family: 'Segoe UI', 'Roboto', 'Arial', sans-serif;

    .view-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 2rem;

        h2 {
            font-size: 1.75rem;
            color: #111827;
            margin: 0;
        }

        .search-input {
            padding: 0.6rem 1rem;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            font-size: 1rem;
            min-width: 300px;
            transition: border-color 0.2s, box-shadow 0.2s;

            &:focus {
                outline: none;
                border-color: #3b82f6;
                box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.2);
            }
        }
    }

    .patient-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
        gap: 1.5rem;
        align-items: stretch;
        grid-auto-rows: 1fr;
    }

    .loading-message,
    .error-message,
    .no-results-message {
        text-align: center;
        padding: 2rem;
        font-size: 1.1rem;
        color: #6b7280;
    }
}

.patient-card {
    background: #ffffff;
    border-radius: 12px;
    border: 1px solid #e5e7eb;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.07), 0 2px 4px -2px rgba(0, 0, 0, 0.07);
    display: flex;
    flex-direction: column;
    transition: transform 0.2s, box-shadow 0.2s;
    height: 100%;
    min-height: unset;

    &:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -4px rgba(0, 0, 0, 0.1);
    }

    .card-header {
        display: flex;
        align-items: center;
        gap: 1rem;
        padding: 1.25rem;
        border-bottom: 1px solid #f3f4f6;

        .patient-avatar {
            font-size: 3rem;
            color: #3b82f6;
            background-color: #dbeafe;
            border-radius: 50%;
            padding: 0.5rem;
            width: 48px;
            height: 48px;
            box-sizing: content-box;
        }

        .patient-info {
            .patient-name {
                font-size: 1.25rem;
                font-weight: 600;
                color: #111827;
                margin: 0 0 0.25rem 0;
            }

            .patient-id {
                font-size: 0.9rem;
                color: #6b7280;
                display: flex;
                align-items: center;
                gap: 0.3rem;
            }
        }
    }

    .card-body {
        padding: 1.25rem;
        flex-grow: 1;
        color: #4b5563;
        font-size: 0.95rem;
        min-height: 0;
    }

    .card-actions {
        padding: 1rem 1.25rem;
        background-color: #f9fafb;
        border-top: 1px solid #f3f4f6;
        display: flex;
        justify-content: flex-end;

        .action-button {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.5rem 1.25rem;
            border-radius: 6px;
            border: 1px solid transparent;
            font-size: 0.95rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s;

            &.primary {
                background-color: #3b82f6;
                color: white;

                &:hover {
                    background-color: #2563eb;
                }
            }
        }
    }
}
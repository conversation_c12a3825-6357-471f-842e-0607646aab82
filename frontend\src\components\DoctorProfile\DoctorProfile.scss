.doctor-profile {
    height: 100vh;
    min-height: 100vh;
    background-color: #f8f9fa;

    &__container {
        display: flex;
        margin-top: 70px;
        height: calc(100vh - 70px);
        // KHÔNG đặt overflow ở đây!
    }

    // Sidebar cố định
    .sidebar {
        position: sticky;
        top: 70px;
        height: calc(100vh - 70px);
        flex-shrink: 0;
        z-index: 10;
        overflow-y: auto;
    }

    &__content {
        flex: 1;
        margin-left: 0;
        padding: 2rem;
        background: #f8f9fa;
        border-radius: 12px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        overflow-y: auto;
        height: 100%;
    }
}

@media (max-width: 768px) {
    .doctor-profile {
        &__container {
            flex-direction: column;
            margin-top: 60px;
        }

        &__content {
            padding: 1rem;
            border-radius: 8px;
        }
    }
}
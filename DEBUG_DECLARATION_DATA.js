// Debug script để kiểm tra dữ liệu frontend gửi lên
const DEBUG_FRONTEND_DATA = `
=== DEBUG: Frontend Data Issue ===

🔍 PHÂN TÍCH VẤN ĐỀ:

Response JSON:
{
    "declarationId": 15,
    "healthNotes": "",
    "symptoms": "",
    "currentMedications": "",
    "allergies": "",
    "emergencyContact": "",
    "emergencyPhone": "",
    "pregnant": false  // ← Lưu ý: "pregnant" thay vì "isPregnant"
}

Các field declaration đều RỖNG dù user có nhập.

🔍 POSSIBLE CAUSES:

1. FRONTEND ISSUE:
   ❌ declarationData có thể null/undefined
   ❌ Frontend không gửi đúng data format
   ❌ Form validation block data

2. BACKEND MAPPING ISSUE:
   ❌ DTO property name mismatch
   ❌ CreateAppointmentWithDeclarationDTO không nhận được data
   ❌ Builder pattern có vấn đề

3. DATABASE ISSUE:
   ❌ Field không được save vào DB
   ❌ Data bị truncate/override

=== DEBUG STEPS ===

Step 1: Kiểm tra Frontend payload
- Mở Network tab trong browser
- Tạo appointment với declaration
- Xem request body có đúng format không

Step 2: Kiểm tra Backend logs
- Thêm log trong AppointmentDeclarationServiceImpl
- Log request object trước khi save
- Log declaration object sau khi build

Step 3: Kiểm tra Database
- Query trực tiếp appointment_declarations table
- Xem data có được insert đúng không

=== EXPECTED REQUEST FORMAT ===

POST /api/appointments/with-declaration
{
    "doctorId": 1,
    "medicalServiceId": 2,
    "appointmentDate": "2025-08-18",
    "appointmentTime": "08:00:00",
    "notes": "khám lần đầu tiên",
    "isPregnant": false,
    "healthNotes": "Sức khỏe tốt",
    "symptoms": "Đau đầu nhẹ",
    "currentMedications": "Paracetamol",
    "allergies": "Dị ứng tôm",
    "emergencyContact": "Nguyễn Văn A", 
    "emergencyPhone": "**********"
}

=== QUICK FIX ===

Thêm logging để debug:
1. Log request trong controller
2. Log declaration data trước save
3. Log saved declaration sau save
`;

console.log(DEBUG_FRONTEND_DATA);

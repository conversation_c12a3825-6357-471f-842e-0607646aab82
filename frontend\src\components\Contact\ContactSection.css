.contact-section {
    background-color: #f8f9fa;
    padding: 80px 0;
}

.section-header {
    margin-bottom: 50px;
}

.section-header h2 {
    color: #333;
    font-size: 2.5rem;
    margin-bottom: 15px;
}

.section-header p {
    color: #666;
    font-size: 1.1rem;
}

.contact-info {
    background: white;
    padding: 30px;
    border-radius: 10px;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
}

.contact-info h3 {
    color: #333;
    margin-bottom: 25px;
    font-size: 1.5rem;
}

.info-item {
    display: flex;
    align-items: flex-start;
    margin-bottom: 20px;
}

.info-item i {
    color: #007bff;
    font-size: 1.2rem;
    margin-right: 15px;
    margin-top: 5px;
}

.info-item p {
    color: #666;
    margin: 0;
    line-height: 1.6;
}

.contact-form {
    background: white;
    padding: 30px;
    border-radius: 10px;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
}

.form-control {
    border: 1px solid #ddd;
    padding: 12px;
    border-radius: 5px;
    margin-bottom: 15px;
}

.form-control:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.btn-primary {
    background-color: #007bff;
    border: none;
    padding: 12px 30px;
    font-size: 1rem;
    border-radius: 5px;
    transition: all 0.3s ease;
}

.btn-primary:hover {
    background-color: #0056b3;
    transform: translateY(-2px);
}

.btn-primary:disabled {
    background-color: #ccc;
    cursor: not-allowed;
}

.alert {
    padding: 15px;
    border-radius: 5px;
    margin-top: 20px;
}

.alert-success {
    background-color: #d4edda;
    border-color: #c3e6cb;
    color: #155724;
}

.alert-danger {
    background-color: #f8d7da;
    border-color: #f5c6cb;
    color: #721c24;
}

/* Responsive styles */
@media (max-width: 768px) {
    .contact-section {
        padding: 40px 0;
    }

    .section-header h2 {
        font-size: 2rem;
    }

    .contact-info,
    .contact-form {
        margin-bottom: 30px;
    }
}
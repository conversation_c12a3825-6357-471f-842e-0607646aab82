// Test doctor leave request functionality
// This test verifies that doctors can send leave requests to admin

const BASE_URL = 'http://localhost:8080';

// Test data for doctor leave request
const testLeaveRequestData = {
    scheduleId: 1, // Should be a valid schedule ID
    doctorId: 1,   // Should be a valid doctor ID
    reason: "Có việc gia đình khẩn cấp"
};

async function testCreateLeaveRequest(requestData, testName) {
    console.log(`\n=== Testing ${testName} ===`);
    console.log('Request data:', JSON.stringify(requestData, null, 2));
    
    try {
        const response = await fetch(`${BASE_URL}/api/doctor-schedule-requests`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': 'Bearer eyJhbGciOiJIUzI1NiJ9.************************************************************************************.YOUR_DOCTOR_TOKEN_HERE'
            },
            body: JSON.stringify(requestData)
        });

        const responseText = await response.text();
        console.log(`Status: ${response.status}`);
        console.log(`Response: ${responseText}`);

        if (response.ok) {
            console.log(`✅ ${testName} - SUCCESS`);
            return JSON.parse(responseText);
        } else {
            console.log(`❌ ${testName} - FAILED`);
            console.log(`Error: ${responseText}`);
            return null;
        }
    } catch (error) {
        console.log(`❌ ${testName} - ERROR: ${error.message}`);
        return null;
    }
}

async function testGetCurrentDoctor() {
    console.log(`\n=== Testing Get Current Doctor ===`);
    
    try {
        const response = await fetch(`${BASE_URL}/api/doctors/me`, {
            method: 'GET',
            headers: {
                'Authorization': 'Bearer eyJhbGciOiJIUzI1NiJ9.************************************************************************************.YOUR_DOCTOR_TOKEN_HERE'
            }
        });

        const responseText = await response.text();
        console.log(`Status: ${response.status}`);
        console.log(`Response: ${responseText}`);

        if (response.ok) {
            console.log(`✅ Get Current Doctor - SUCCESS`);
            return JSON.parse(responseText);
        } else {
            console.log(`❌ Get Current Doctor - FAILED`);
            return null;
        }
    } catch (error) {
        console.log(`❌ Get Current Doctor - ERROR: ${error.message}`);
        return null;
    }
}

async function testGetDoctorSchedule() {
    console.log(`\n=== Testing Get Doctor Schedule ===`);
    
    try {
        const response = await fetch(`${BASE_URL}/api/doctors/me/schedule-dto`, {
            method: 'GET',
            headers: {
                'Authorization': 'Bearer eyJhbGciOiJIUzI1NiJ9.************************************************************************************.YOUR_DOCTOR_TOKEN_HERE'
            }
        });

        const responseText = await response.text();
        console.log(`Status: ${response.status}`);
        console.log(`Response: ${responseText}`);

        if (response.ok) {
            console.log(`✅ Get Doctor Schedule - SUCCESS`);
            const schedules = JSON.parse(responseText);
            if (schedules.length > 0) {
                console.log(`Found ${schedules.length} schedule(s). First schedule ID: ${schedules[0].id}`);
                return schedules;
            }
            return schedules;
        } else {
            console.log(`❌ Get Doctor Schedule - FAILED`);
            return null;
        }
    } catch (error) {
        console.log(`❌ Get Doctor Schedule - ERROR: ${error.message}`);
        return null;
    }
}

async function runDoctorLeaveRequestTests() {
    console.log("🧪 Doctor Leave Request Tests");
    console.log("==============================");
    
    // Test 1: Get current doctor info
    const doctorInfo = await testGetCurrentDoctor();
    if (!doctorInfo) {
        console.log("❌ Cannot proceed without doctor info");
        return;
    }
    
    // Test 2: Get doctor schedules
    const schedules = await testGetDoctorSchedule();
    if (!schedules || schedules.length === 0) {
        console.log("❌ Cannot proceed without schedule data");
        return;
    }
    
    // Test 3: Create leave request with real data
    const realRequestData = {
        scheduleId: schedules[0].id,
        doctorId: doctorInfo.id,
        reason: "Test leave request - có việc gia đình khẩn cấp"
    };
    
    await testCreateLeaveRequest(realRequestData, "Real Leave Request");
    
    // Test 4: Test with invalid schedule ID
    const invalidScheduleData = {
        scheduleId: 99999,
        doctorId: doctorInfo.id,
        reason: "Test with invalid schedule"
    };
    
    await testCreateLeaveRequest(invalidScheduleData, "Invalid Schedule ID");
    
    // Test 5: Test with missing reason
    const missingReasonData = {
        scheduleId: schedules[0].id,
        doctorId: doctorInfo.id,
        reason: ""
    };
    
    await testCreateLeaveRequest(missingReasonData, "Missing Reason");
    
    console.log("\n🏁 Tests completed!");
    console.log("\nExpected Results:");
    console.log("✅ Get Current Doctor: Should return doctor info with ID");
    console.log("✅ Get Doctor Schedule: Should return list of schedules");
    console.log("✅ Real Leave Request: Should succeed if schedule and doctor exist");
    console.log("❌ Invalid Schedule ID: May fail if schedule doesn't exist");
    console.log("❌ Missing Reason: May fail validation if reason is required");
    
    console.log("\nCommon Issues to Check:");
    console.log("1. Doctor role authentication - ensure token has DOCTOR role");
    console.log("2. Schedule existence - ensure scheduleId exists in database");
    console.log("3. Doctor existence - ensure doctorId exists in database");
    console.log("4. Required fields - ensure all required fields are provided");
}

// Helper function to test specific scenarios
async function debugLeaveRequestIssue() {
    console.log("\n🔍 Debugging Leave Request Issue");
    console.log("=================================");
    
    console.log("Checking step by step...");
    
    // Step 1: Check authentication
    console.log("\n1. Testing authentication...");
    await testGetCurrentDoctor();
    
    // Step 2: Check doctor schedule access
    console.log("\n2. Testing schedule access...");
    await testGetDoctorSchedule();
    
    // Step 3: Test simple request
    console.log("\n3. Testing simple leave request...");
    const simpleData = {
        scheduleId: 1,
        doctorId: 1,
        reason: "Test"
    };
    await testCreateLeaveRequest(simpleData, "Simple Test Request");
}

// Uncomment to run all tests
// runDoctorLeaveRequestTests();

// Uncomment to debug specific issue
// debugLeaveRequestIssue();

// Export for manual testing
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        testCreateLeaveRequest,
        testGetCurrentDoctor,
        testGetDoctorSchedule,
        runDoctorLeaveRequestTests,
        debugLeaveRequestIssue
    };
}

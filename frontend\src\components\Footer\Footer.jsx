import React from 'react';
import './Footer.scss';
import logo from '../../assets/SWPLogo.png';
import { FaPhoneAlt, FaEnvelope, FaMapMarkerAlt } from 'react-icons/fa';

const Footer = () => {
    // Đường dẫn logo có thể được truyền qua props hoặc đặt mặc định
    const logoUrl = "/images/logo.png"; // Thay đổi đường dẫn này theo ý bạn

    return (
        <footer className="footer">
            <div className="container">
                <div className="footer-content">
                    <div className="footer-section">
                        <div className="logo">
                            <img src={logo} alt="HIV Treatment Management Logo" className="logo" />
                            <span className="logo-text">HIV Care</span>
                        </div>
                        <p>Chăm sóc sức khỏe HIV toàn diện với đội ngũ chuyên nghiệp và tận tâm.</p>
                    </div>

                    <div className="footer-section">
                        <h3><PERSON><PERSON><PERSON> kế<PERSON> n<PERSON></h3>
                        <ul>
                            <li><a href="/">Trang chủ</a></li>
                            <li><a href="#services">Dịch vụ</a></li>
                            <li><a href="/about">Về chúng tôi</a></li>
                            <li><a href="#contact">Liên hệ</a></li>
                        </ul>
                    </div>

                    <div className="footer-section">
                        <h3>Dịch vụ</h3>
                        <ul>
                            <li><a href="#booking">Đặt lịch khám</a></li>
                            <li><a href="#results">Kết quả xét nghiệm</a></li>
                            <li><a href="#consultation">Tư vấn trực tuyến</a></li>
                            <li><a href="#support">Hỗ trợ cộng đồng</a></li>
                        </ul>
                    </div>

                    <div className="footer-section">
                        <h3>Liên hệ</h3>
                        <div className="contact-info">
                            <p><FaPhoneAlt className="icon" /> Hotline: <a href="tel:19009095">19009095</a></p>
                            <p><FaEnvelope className="icon" /> Email: <a href="mailto:<EMAIL>"><EMAIL></a></p>
                            <p><FaMapMarkerAlt className="icon" /> Địa chỉ: 123 Đường ABC, Quận 1, TP.HCM</p>
                        </div>
                    </div>
                </div>

                <div className="footer-bottom">
                    <div className="footer-chat">
                        <div className="chat-widget">
                            <span>💬</span>
                            <div className="chat-content">
                                <p>Bạn cần hỗ trợ gì không?</p>
                                <small>Chúng tôi luôn sẵn sàng hỗ trợ bạn 24/7</small>
                            </div>
                        </div>
                    </div>
                    <div className="copyright">
                        <p>&copy; 2024 HIV Care. Tất cả quyền được bảo lưu.</p>
                    </div>
                </div>
            </div>
        </footer>
    );
};

export default Footer;
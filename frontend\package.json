{"name": "demo2", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@fortawesome/fontawesome-svg-core": "^6.7.2", "@fortawesome/free-solid-svg-icons": "^6.7.2", "@fortawesome/react-fontawesome": "^0.2.2", "axios": "^1.9.0", "dayjs": "^1.11.13", "emoji-mart": "^5.6.0", "framer-motion": "^12.15.0", "js-cookies": "^1.0.4", "lucide-react": "^0.517.0", "react": "^19.1.0", "react-confirm-alert": "^3.0.6", "react-dom": "^19.1.0", "react-fontawesome": "^1.7.1", "react-icons": "^5.5.0", "react-pro-sidebar": "^1.1.0", "react-router-dom": "^7.6.1", "react-toastify": "^11.0.5", "recharts": "^3.1.0", "sass": "^1.89.1", "styled-components": "^6.1.18"}, "devDependencies": {"@eslint/js": "^9.25.0", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@vitejs/plugin-react": "^4.4.1", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "vite": "^6.3.5"}}
// Test tạo khai báo y tế khi bệnh nhân đặt lịch
const API_BASE = 'http://localhost:8080/api';

async function testAppointmentWithDeclaration() {
    try {
        console.log('🏥 Testing Appointment Creation with Medical Declaration...');

        // Test case 1: Tạo appointment thông thường (không có declaration)
        console.log('\n=== Test 1: Appointment thông thường (KHÔNG có declaration) ===');
        
        const normalAppointment = {
            doctorId: 1,
            medicalServiceId: 1,
            appointmentDate: "2025-08-10",
            appointmentTime: "08:00:00",
            notes: "Test appointment without declaration"
        };

        console.log('📤 Gửi request tạo appointment thông thường...');
        const normalResponse = await fetch(`${API_BASE}/appointments`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': 'Bearer test-token' // Cần token thật để test
            },
            body: JSON.stringify(normalAppointment)
        });

        console.log('📡 Response status:', normalResponse.status);
        if (normalResponse.status === 403) {
            console.log('🔒 Cần login để test API này');
        } else if (normalResponse.ok) {
            const appointmentData = await normalResponse.json();
            console.log('✅ Appointment tạo thành công:', appointmentData);
            
            // Kiểm tra xem có declaration nào cho appointment này không
            console.log('\n🔍 Kiểm tra có declaration cho appointment này không...');
            const declarationResponse = await fetch(`${API_BASE}/appointment-declarations/appointment/${appointmentData.id}`);
            
            if (declarationResponse.status === 404) {
                console.log('❌ KHÔNG có declaration cho appointment thông thường (đúng như mong đợi)');
            } else if (declarationResponse.ok) {
                const declaration = await declarationResponse.json();
                console.log('⚠️ Có declaration cho appointment thông thường:', declaration);
            }
        }

        console.log('\n=== Test 2: Appointment với declaration (endpoint mới) ===');
        
        const appointmentWithDeclaration = {
            // Thông tin appointment
            doctorId: 1,
            medicalServiceId: 1,
            appointmentDate: "2025-08-11",
            appointmentTime: "09:00:00",
            notes: "Test appointment with declaration",
            
            // Thông tin khai báo sức khỏe
            isPregnant: false,
            healthNotes: "Sức khỏe tốt",
            symptoms: "Đau đầu nhẹ",
            currentMedications: "Không có",
            allergies: "Dị ứng tôm",
            emergencyContact: "Nguyễn Văn A",
            emergencyPhone: "**********"
        };

        console.log('📤 Gửi request tạo appointment kèm declaration...');
        const withDeclarationResponse = await fetch(`${API_BASE}/appointments/with-declaration`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': 'Bearer test-token' // Cần token thật để test
            },
            body: JSON.stringify(appointmentWithDeclaration)
        });

        console.log('📡 Response status:', withDeclarationResponse.status);
        if (withDeclarationResponse.status === 403) {
            console.log('🔒 Cần login để test API này');
        } else if (withDeclarationResponse.ok) {
            const responseData = await withDeclarationResponse.json();
            console.log('✅ Appointment + Declaration tạo thành công:', responseData);
            console.log('📋 Thông tin declaration:');
            console.log('   - Declaration ID:', responseData.declarationId);
            console.log('   - Mang thai:', responseData.isPregnant ? 'Có' : 'Không');
            console.log('   - Triệu chứng:', responseData.symptoms);
            console.log('   - Dị ứng:', responseData.allergies);
            console.log('   - Liên hệ khẩn cấp:', responseData.emergencyContact);
        } else {
            const error = await withDeclarationResponse.text();
            console.log('❌ Lỗi khi tạo appointment với declaration:', error);
        }

        console.log('\n=== Phân tích luồng frontend hiện tại ===');
        console.log('🔄 AppointmentForm.jsx:');
        console.log('   ✅ Có AppointmentDeclarationForm component');
        console.log('   ✅ Hiển thị form khai báo trước khi submit');
        console.log('   ✅ Tạo appointment trước, sau đó tạo declaration riêng');
        console.log('   ❌ CHƯA sử dụng endpoint /appointments/with-declaration');

        console.log('\n📊 Backend endpoints:');
        console.log('   ✅ POST /api/appointments (tạo appointment thông thường)');
        console.log('   ✅ POST /api/appointment-declarations (tạo declaration riêng)');
        console.log('   ✅ POST /api/appointments/with-declaration (tạo cùng lúc)');

        console.log('\n🎯 Vấn đề tiềm ẩn:');
        console.log('   ⚠️ Nếu tạo appointment thành công nhưng declaration fail:');
        console.log('      → Appointment tồn tại nhưng không có thông tin sức khỏe');
        console.log('   ⚠️ Frontend có thể bỏ qua lỗi declaration (không fail toàn bộ)');
        
        console.log('\n💡 Khuyến nghị:');
        console.log('   🔄 Chuyển sang sử dụng /appointments/with-declaration');
        console.log('   🔄 Để đảm bảo tính atomic (tạo cùng lúc hoặc fail cùng lúc)');

    } catch (error) {
        console.log('❌ Network error:', error.message);
    }
}

// Chạy test
testAppointmentWithDeclaration();

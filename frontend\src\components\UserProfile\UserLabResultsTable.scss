.user-lab-results-table table {
    width: 100%;
    border-collapse: collapse;
    background: #fff;
    border-radius: 8px;
    overflow: hidden;
    table-layout: fixed;
    /* Optional: Uncomment the next line if you want an outer border */
    /* border: 1px solid #e0e0e0; */
}

.user-lab-results-table th,
.user-lab-results-table td {
    padding: 0.75rem 1rem;
    border: none; /* Remove all column borders */
    text-align: left;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.lab-results-table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 16px rgba(0,0,0,0.07);
  overflow: hidden;
  margin-top: 24px;

  th, td {
    padding: 12px 16px;
    text-align: left;
  }

  th {
    background: #f5f7fa;
    color: #222;
    font-weight: 600;
    border-bottom: 2px solid #e0e6ed;
  }

  tr {
    transition: background 0.2s;
  }

  tbody tr:hover {
    background: #f0f4fa;
  }

  td {
    border-bottom: 1px solid #f0f0f0;
    vertical-align: middle;
    font-size: 15px;
  }

  tr:last-child td {
    border-bottom: none;
  }

  // Tăng độ rộng cột Kết quả, giảm cột Giá
  th:nth-child(4), td:nth-child(4) {
    min-width: 220px;
    max-width: 340px;
    width: 28%;
    white-space: normal;
    word-break: break-word;
  }
  th:nth-child(6), td:nth-child(6) {
    min-width: 90px;
    max-width: 120px;
    width: 8%;
    text-align: right;
    font-weight: 600;
  }
}

@media (max-width: 800px) {
  .lab-results-table th, .lab-results-table td {
    padding: 8px 6px;
    font-size: 13px;
  }
  .lab-results-table th:nth-child(4), .lab-results-table td:nth-child(4) {
    min-width: 120px;
    max-width: 200px;
    width: 30%;
  }
  .lab-results-table th:nth-child(6), .lab-results-table td:nth-child(6) {
    min-width: 60px;
    max-width: 80px;
    width: 10%;
  }
} 
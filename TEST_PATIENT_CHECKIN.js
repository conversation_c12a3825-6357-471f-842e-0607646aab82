/**
 * Test API cho chức năng bệnh nhân check-in lịch khám
 * 
 * Endpoint: POST /api/appointments/{id}/checkin
 * Authorization: B<PERSON><PERSON> nhân (ROLE_PATIENT)
 * 
 * Flow Test:
 * 1. Tạo appointment với status CONFIRMED
 * 2. <PERSON><PERSON><PERSON> nhân check-in (CONFIRMED -> CHECKED_IN)
 * 3. Verify trạng thái đã thay đổi
 * 4. Verify email notification đượ<PERSON> gửi cho bác sĩ
 */

// Test data
const testData = {
    // Thông tin appointment để test check-in
    appointment: {
        id: 1, // Thay bằng ID appointment thực tế có status CONFIRMED
        patientId: 1, // ID bệnh nhân sở hữu appointment
        doctorId: 1,
        appointmentDate: "2025-08-03", // Ngày hôm nay để có thể check-in
        appointmentTime: "14:00", // Thời gian cho phép check-in
        status: "CONFIRMED" // Phải là CONFIRMED mới check-in đ<PERSON><PERSON><PERSON>
    },
    
    // Thông tin user để authentication
    patient: {
        email: "<EMAIL>", // Email bệnh nhân đã đăng ký
        password: "password123"
    }
};

// API endpoints
const API_BASE = "http://localhost:8080/api";
const endpoints = {
    login: `${API_BASE}/auth/login`,
    patientCheckIn: (appointmentId) => `${API_BASE}/appointments/${appointmentId}/checkin`,
    getAppointment: (appointmentId) => `${API_BASE}/appointments/${appointmentId}`,
    getMyAppointments: `${API_BASE}/appointments/patient/me`
};

console.log("=== Test: Patient Check-in for Confirmed Appointment ===");
console.log("Test data:", JSON.stringify(testData, null, 2));
console.log("API endpoints:", endpoints);

console.log("\nSteps to test:");
console.log("1. Login as patient to get JWT token");
console.log("2. Get appointment details (should be CONFIRMED status)");
console.log("3. Patient check-in appointment");
console.log("4. Verify appointment status changed to CHECKED_IN");
console.log("5. Check if doctor receives email notification");

console.log("\n=== API Details ===");
console.log("Endpoint: POST /api/appointments/{id}/checkin");
console.log("Authorization: Bearer {jwt_token} (ROLE_PATIENT)");
console.log("Request Body: None (patientId extracted from authentication)");
console.log("Response: AppointmentResponseDTO with updated status");

console.log("\n=== Business Rules ===");
console.log("✓ Chỉ bệnh nhân sở hữu appointment mới được check-in");
console.log("✓ Chỉ check-in được appointment có status CONFIRMED");
console.log("✓ Chỉ check-in được trong ngày hẹn");
console.log("✓ Thời gian check-in: từ 30 phút trước giờ hẹn đến 1 giờ sau giờ hẹn");
console.log("✓ Tự động gửi email thông báo cho bác sĩ khi check-in thành công");
console.log("✓ Thêm note về thời gian check-in vào appointment");

console.log("\n=== Error Cases ===");
console.log("❌ 403 Forbidden: Bệnh nhân khác cố gắng check-in");
console.log("❌ 400 Bad Request: Status không phải CONFIRMED");
console.log("❌ 400 Bad Request: Không đúng ngày hẹn");
console.log("❌ 400 Bad Request: Chưa đến thời gian check-in hoặc đã quá thời gian");

console.log("\n=== Sample Request ===");
console.log(`
// 1. Login request
POST ${endpoints.login}
Content-Type: application/json
{
    "email": "${testData.patient.email}",
    "password": "${testData.patient.password}"
}

// 2. Check-in request
POST ${endpoints.patientCheckIn(testData.appointment.id)}
Authorization: Bearer {jwt_token}
Content-Type: application/json
// No body required

// 3. Expected Response
{
    "id": ${testData.appointment.id},
    "status": "CHECKED_IN",
    "notes": "Bệnh nhân đã check-in lúc 14:30 ngày 2025-08-03",
    "appointmentDate": "${testData.appointment.appointmentDate}",
    "appointmentTime": "${testData.appointment.appointmentTime}",
    // ... other fields
}
`);

console.log("\n=== Implementation Details ===");
console.log("- Service method: AppointmentServiceImpl.patientCheckIn()");
console.log("- Controller endpoint: AppointmentController.patientCheckIn()");
console.log("- Email service: EmailNotificationServiceImpl.sendPatientCheckInNotification()");
console.log("- Authorization: @PreAuthorize('hasRole(PATIENT)')");
console.log("- Transaction: @Transactional for data consistency");

console.log("\n=== Expected Email Notification ===");
console.log("To: <EMAIL>");
console.log("Subject: Thông báo bệnh nhân đã check-in");
console.log("Content: Bác sĩ [Name] - Bệnh nhân [Name] đã check-in cho lịch khám [Service] ngày [Date] lúc [Time]");

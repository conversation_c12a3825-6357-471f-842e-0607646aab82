# Troubleshooting Guide - Frontend Import Errors

## Lỗi: "The requested module does not provide an export named 'default'"

### Nguyên nhân:
1. File component không có `export default`
2. Cache của development server hoặc browser
3. Syntax error trong file component
4. Hot reload không hoạt động đúng

### Giải pháp:

#### 1. Kiểm tra export statement
Đảm bảo file component có export default ở cuối:
```jsx
const ComponentName = () => {
    // component code
};

export default ComponentName;
```

#### 2. Restart Development Server
```bash
# Trong terminal đang chạy frontend
Ctrl + C  # Dừng server

# Xóa cache và restart
npm run dev
```

#### 3. Clear Browser Cache
- **Chrome/Edge**: Ctrl + Shift + R (hard refresh)
- **Firefox**: Ctrl + F5
- Hoặc mở Developer Tools > Application > Storage > Clear storage

#### 4. Clear Vite Cache
```bash
cd frontend
rm -rf node_modules/.vite  # Linux/Mac
rmdir /s /q node_modules\.vite  # Windows
npm run dev
```

#### 5. Restart cả Backend và Frontend
```bash
# Terminal 1 - Backend
cd backend/hiv-treatment-medical-service-system
mvn spring-boot:run

# Terminal 2 - Frontend  
cd frontend
npm run dev
```

### Kiểm tra các file component đã fix:
- ✅ PatientMedication.jsx - Đã thêm export default
- ✅ PatientTreatmentPlans.jsx - Có export default
- ✅ PatientMedicationReminders.jsx - Có export default

### Lệnh nhanh để restart:
Chạy file `restart_frontend.bat` trong thư mục gốc của project.

### Nếu vẫn lỗi:
1. Kiểm tra console log để xem lỗi cụ thể
2. Kiểm tra import path có đúng không
3. Kiểm tra tên file có đúng case không (PatientMedication.jsx vs patientmedication.jsx)
4. Restart VSCode và terminal

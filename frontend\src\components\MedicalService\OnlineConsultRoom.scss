.consult-room-wrapper {

    display: flex;
    gap: 32px;
    min-height: 80vh;
    background: #fafbfc;
    border-radius: 18px;
    padding: 32px 16px;
    margin-top: 50px;

    .consult-sidebar {
        flex: 0 0 320px;
        background: #fff;
        border-radius: 12px;
        padding: 24px 18px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
        display: flex;
        flex-direction: column;
        gap: 32px;

        h2 {
            font-size: 1.3rem;
            font-weight: 700;
            margin-bottom: 12px;
        }

        .doctor-list {
            display: flex;
            flex-direction: column;
            gap: 14px;

            .doctor-item {
                display: flex;
                align-items: center;
                gap: 12px;
                padding: 10px 8px;
                border-radius: 8px;
                cursor: pointer;
                transition: background 0.15s;

                &.selected,
                &:hover {
                    background: #f0f4ff;
                }

                .icon {
                    font-size: 1.6rem;
                    color: #007bff;
                }

                .name {
                    font-weight: 600;
                    color: #222;
                }

                .status {
                    font-size: 0.95rem;

                    .online {
                        color: #1bc47d;
                        font-weight: 600;
                    }

                    .busy {
                        color: #e57373;
                        font-weight: 600;
                    }
                }
            }
        }

        .upcoming-call {
            h3 {
                font-size: 1.1rem;
                font-weight: 600;
                margin-bottom: 10px;
            }

            .call-card {
                display: flex;
                align-items: center;
                gap: 12px;
                background: #f5f7fa;
                border-radius: 8px;
                padding: 10px 12px;
                margin-bottom: 10px;

                .icon {
                    font-size: 1.5rem;
                    color: #007bff;
                }

                .call-title {
                    font-weight: 600;
                }

                .call-doctor {
                    font-size: 0.98rem;
                    color: #333;
                }

                .call-time {
                    font-size: 0.95rem;
                    color: #666;
                    display: flex;
                    align-items: center;
                    gap: 4px;
                }

                .join-btn {
                    background: #007bff;
                    color: #fff;
                    border: none;
                    border-radius: 6px;
                    padding: 6px 14px;
                    font-size: 0.98rem;
                    font-weight: 600;
                    cursor: pointer;
                    margin-left: auto;
                }
            }

            .schedule-btn {
                width: 100%;
                background: #181818;
                color: #fff;
                border: none;
                border-radius: 8px;
                padding: 10px 0;
                font-size: 1rem;
                font-weight: 600;
                cursor: pointer;
                margin-top: 6px;
                transition: background 0.2s;

                &:hover {
                    background: #007bff;
                }
            }
        }
    }

    .consult-chat-area {
        flex: 1;
        background: #fff;
        border-radius: 12px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
        display: flex;
        flex-direction: column;
        padding: 0 0 0 0;
        min-width: 0;

        .chat-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 20px 24px 10px 24px;
            border-bottom: 1px solid #f0f0f0;

            .doctor-info {
                display: flex;
                align-items: center;
                gap: 12px;

                .icon {
                    font-size: 1.5rem;
                    color: #007bff;
                }

                .name {
                    font-weight: 600;
                    color: #222;
                }

                .status {
                    font-size: 0.95rem;
                    color: #1bc47d;
                }
            }

            .chat-actions {
                display: flex;
                gap: 8px;

                .anon-btn {
                    background: #f5f7fa;
                    border: none;
                    border-radius: 6px;
                    padding: 7px 10px;
                    font-size: 1.1rem;
                    cursor: pointer;
                    color: #222;

                    &.active {
                        background: #007bff;
                        color: #fff;
                    }
                }

                .icon-btn {
                    background: #f5f7fa;
                    border: none;
                    border-radius: 6px;
                    padding: 7px 10px;
                    font-size: 1.1rem;
                    cursor: pointer;
                    color: #222;
                }
            }
        }

        .chat-tabs {
            display: flex;
            border-bottom: 1px solid #f0f0f0;

            button {
                flex: 1;
                background: none;
                border: none;
                font-size: 1rem;
                font-weight: 600;
                padding: 12px 0;
                cursor: pointer;
                color: #888;
                display: flex;
                align-items: center;
                gap: 6px;

                &.active {
                    color: #007bff;
                    border-bottom: 2.5px solid #007bff;
                    background: #f5f7fa;
                }
            }
        }

        .chat-body {
            flex: 1;
            overflow-y: auto;
            padding: 24px;
            background: #f9fbfc;
            min-height: 300px;
            max-height: 500px;

            .messages {
                display: flex;
                flex-direction: column;
                gap: 14px;

                .message {
                    max-width: 70%;
                    padding: 10px 16px;
                    border-radius: 12px;
                    font-size: 1rem;
                    background: #f5f7fa;
                    color: #222;
                    align-self: flex-start;
                    position: relative;

                    .msg-content {
                        margin-bottom: 4px;
                    }

                    .msg-time {
                        font-size: 0.85rem;
                        color: #888;
                        text-align: right;
                    }

                    &.user {
                        background: #181818;
                        color: #fff;
                        align-self: flex-end;
                    }

                    &.anonymous {
                        background: #007bff;
                        color: #fff;
                        align-self: flex-end;
                    }
                }
            }

            .history-placeholder {
                color: #888;
                text-align: center;
                margin-top: 40px;
            }
        }

        .chat-input-area {
            display: flex;
            align-items: center;
            padding: 16px 24px;
            border-top: 1px solid #f0f0f0;
            background: #fff;

            input[type="text"] {
                flex: 1;
                border: 1px solid #e0e0e0;
                border-radius: 8px;
                padding: 10px 16px;
                font-size: 1rem;
                margin-right: 12px;
                outline: none;
                transition: border 0.2s;

                &:focus {
                    border: 1.5px solid #007bff;
                }
            }

            .send-btn {
                background: #007bff;
                color: #fff;
                border: none;
                border-radius: 8px;
                padding: 10px 18px;
                font-size: 1.1rem;
                cursor: pointer;
                transition: background 0.2s;

                &:hover {
                    background: #181818;
                }
            }
        }
    }
}

@media (max-width: 1100px) {
    .consult-room-wrapper {
        flex-direction: column;
        gap: 24px;

        .consult-sidebar {
            width: 100%;
            flex: none;
            margin-bottom: 12px;
        }

        .consult-chat-area {
            width: 100%;
            min-width: 0;
        }
    }
}

@media (max-width: 700px) {
    .consult-room-wrapper {
        padding: 8px 2px;

        .consult-sidebar,
        .consult-chat-area {
            padding: 10px 4px;
        }

        .consult-chat-area .chat-header,
        .consult-chat-area .chat-input-area {
            padding: 10px 8px;
        }

        .consult-chat-area .chat-body {
            padding: 10px 4px;
        }
    }
}